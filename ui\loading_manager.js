/**
 * LOADING MANAGER
 * Handles all loading animations, progress indicators, and user feedback
 */

class LoadingManager {
    constructor() {
        this.activeLoaders = new Set();
        this.progressCallbacks = new Map();
        this.toastQueue = [];
        this.isShowingToast = false;

        console.log('⚡ LoadingManager initialized');
        this.initialize();
    }

    /**
     * Initialize loading manager
     */
    initialize() {
        this.createLoadingOverlay();
        console.log('✅ LoadingManager initialization complete');
    }

    /**
     * Create the main loading overlay
     */
    createLoadingOverlay() {
        // Remove existing overlay if present
        const existing = document.getElementById('loading-overlay');
        if (existing) {
            existing.remove();
        }

        const overlay = document.createElement('div');
        overlay.id = 'loading-overlay';
        overlay.className = 'loading-overlay';
        
        overlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text" id="loading-text">Loading...</div>
                <div class="loading-subtext" id="loading-subtext">Please wait while we process your request</div>
                <div class="progress-bar" id="progress-bar" style="display: none;">
                    <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                </div>
                <div class="progress-text" id="progress-text" style="display: none;">0%</div>
            </div>
        `;
        
        document.body.appendChild(overlay);
    }

    /**
     * Show loading overlay with custom message
     */
    showLoading(options = {}) {
        const {
            text = 'Loading...',
            subtext = 'Please wait while we process your request',
            showProgress = false,
            id = 'default'
        } = options;

        this.activeLoaders.add(id);

        const overlay = document.getElementById('loading-overlay');
        const textEl = document.getElementById('loading-text');
        const subtextEl = document.getElementById('loading-subtext');
        const progressBar = document.getElementById('progress-bar');
        const progressText = document.getElementById('progress-text');

        if (overlay && textEl && subtextEl) {
            textEl.textContent = text;
            subtextEl.textContent = subtext;
            
            if (showProgress) {
                progressBar.style.display = 'block';
                progressText.style.display = 'block';
                this.updateProgress(0, id);
            } else {
                progressBar.style.display = 'none';
                progressText.style.display = 'none';
            }

            overlay.classList.add('active');
        }

        console.log(`🔄 Loading started: ${id} - ${text}`);
    }

    /**
     * Update progress for a specific loader
     */
    updateProgress(percentage, id = 'default', message = '') {
        if (!this.activeLoaders.has(id)) return;

        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        const loadingText = document.getElementById('loading-text');

        if (progressFill && progressText) {
            progressFill.style.width = `${Math.min(100, Math.max(0, percentage))}%`;
            progressText.textContent = `${Math.round(percentage)}%`;
            
            if (message) {
                loadingText.textContent = message;
            }
        }

        // Call progress callback if registered
        const callback = this.progressCallbacks.get(id);
        if (callback) {
            callback(percentage, message);
        }

        console.log(`📊 Progress updated: ${id} - ${percentage}%`);
    }

    /**
     * Hide loading overlay
     */
    hideLoading(id = 'default') {
        this.activeLoaders.delete(id);
        this.progressCallbacks.delete(id);

        // Only hide overlay if no other loaders are active
        if (this.activeLoaders.size === 0) {
            const overlay = document.getElementById('loading-overlay');
            if (overlay) {
                overlay.classList.remove('active');
            }
        }

        console.log(`✅ Loading finished: ${id}`);
    }

    /**
     * Show button loading state
     */
    showButtonLoading(buttonElement, text = '') {
        if (!buttonElement) return;

        buttonElement.classList.add('btn-loading');
        buttonElement.disabled = true;

        // Store original content
        if (!buttonElement.dataset.originalContent) {
            buttonElement.dataset.originalContent = buttonElement.innerHTML;
        }

        if (text) {
            buttonElement.innerHTML = `<span class="btn-text">${text}</span>`;
        }

        console.log('🔄 Button loading state activated');
    }

    /**
     * Hide button loading state
     */
    hideButtonLoading(buttonElement) {
        if (!buttonElement) return;

        buttonElement.classList.remove('btn-loading');
        buttonElement.disabled = false;

        // Restore original content
        if (buttonElement.dataset.originalContent) {
            buttonElement.innerHTML = buttonElement.dataset.originalContent;
            delete buttonElement.dataset.originalContent;
        }

        console.log('✅ Button loading state deactivated');
    }

    /**
     * Show success animation
     */
    showSuccess(options = {}) {
        const {
            title = 'Success!',
            message = 'Operation completed successfully',
            duration = 3000
        } = options;

        this.hideLoading();

        const overlay = document.getElementById('loading-overlay');
        const content = overlay.querySelector('.loading-content');

        content.innerHTML = `
            <div class="success-checkmark animate"></div>
            <div class="loading-text">${title}</div>
            <div class="loading-subtext">${message}</div>
        `;

        overlay.classList.add('active');

        setTimeout(() => {
            overlay.classList.remove('active');
            this.createLoadingOverlay(); // Restore original content
        }, duration);

        console.log(`✅ Success shown: ${title}`);
    }

    /**
     * Show error animation
     */
    showError(options = {}) {
        const {
            title = 'Error',
            message = 'An error occurred. Please try again.',
            duration = 4000
        } = options;

        this.hideLoading();

        const overlay = document.getElementById('loading-overlay');
        const content = overlay.querySelector('.loading-content');

        content.innerHTML = `
            <div class="error-icon animate"></div>
            <div class="loading-text">${title}</div>
            <div class="loading-subtext">${message}</div>
            <button onclick="window.loadingManager.hideError()" style="margin-top: 15px; padding: 8px 16px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">Close</button>
        `;

        overlay.classList.add('active');

        // Auto-hide after duration
        setTimeout(() => {
            if (overlay.classList.contains('active')) {
                this.hideError();
            }
        }, duration);

        console.log(`❌ Error shown: ${title}`);
    }

    /**
     * Hide error overlay
     */
    hideError() {
        const overlay = document.getElementById('loading-overlay');
        overlay.classList.remove('active');
        this.createLoadingOverlay(); // Restore original content
    }

    /**
     * Show toast notification
     */
    showToast(options = {}) {
        const {
            type = 'info', // success, error, info
            title = '',
            message = '',
            duration = 4000
        } = options;

        const toast = {
            id: Date.now(),
            type,
            title,
            message,
            duration
        };

        this.toastQueue.push(toast);
        this.processToastQueue();
    }

    /**
     * Process toast queue
     */
    processToastQueue() {
        if (this.isShowingToast || this.toastQueue.length === 0) return;

        const toast = this.toastQueue.shift();
        this.isShowingToast = true;

        this.displayToast(toast);
    }

    /**
     * Display individual toast
     */
    displayToast(toast) {
        // Remove existing toast
        const existing = document.getElementById('toast-notification');
        if (existing) {
            existing.remove();
        }

        const toastEl = document.createElement('div');
        toastEl.id = 'toast-notification';
        toastEl.className = `toast-notification ${toast.type}`;
        
        toastEl.innerHTML = `
            ${toast.title ? `<div class="toast-title">${toast.title}</div>` : ''}
            <div class="toast-message">${toast.message}</div>
        `;

        document.body.appendChild(toastEl);

        // Show toast
        setTimeout(() => {
            toastEl.classList.add('show');
        }, 100);

        // Hide toast
        setTimeout(() => {
            toastEl.classList.remove('show');
            setTimeout(() => {
                if (toastEl.parentNode) {
                    toastEl.remove();
                }
                this.isShowingToast = false;
                this.processToastQueue(); // Process next toast
            }, 300);
        }, toast.duration);

        console.log(`📢 Toast shown: ${toast.type} - ${toast.message}`);
    }

    /**
     * Show skeleton loading for data
     */
    showSkeleton(container, options = {}) {
        const {
            lines = 3,
            animate = true
        } = options;

        if (!container) return;

        const skeletonHTML = Array(lines).fill(0).map((_, index) => {
            const widthClass = index % 3 === 0 ? 'long' : index % 3 === 1 ? 'medium' : 'short';
            return `<div class="skeleton skeleton-text ${widthClass}"></div>`;
        }).join('');

        container.innerHTML = `<div class="skeleton-container">${skeletonHTML}</div>`;
        
        if (animate) {
            container.classList.add('fade-in');
        }

        console.log(`💀 Skeleton loading shown in container`);
    }

    /**
     * Hide skeleton loading
     */
    hideSkeleton(container, content = '') {
        if (!container) return;

        container.innerHTML = content;
        container.classList.add('fade-in');

        console.log(`✅ Skeleton loading hidden`);
    }

    /**
     * Register progress callback
     */
    onProgress(id, callback) {
        this.progressCallbacks.set(id, callback);
    }

    /**
     * Simulate progress for demo purposes
     */
    simulateProgress(id = 'default', duration = 3000) {
        let progress = 0;
        const interval = 50;
        const increment = (100 / duration) * interval;

        const timer = setInterval(() => {
            progress += increment;
            
            if (progress >= 100) {
                this.updateProgress(100, id, 'Complete!');
                clearInterval(timer);
            } else {
                this.updateProgress(progress, id);
            }
        }, interval);

        return timer;
    }

    /**
     * Add fade-in animation to element
     */
    fadeIn(element) {
        if (element) {
            element.classList.add('fade-in');
        }
    }

    /**
     * Add slide-in animation to element
     */
    slideIn(element) {
        if (element) {
            element.classList.add('slide-in');
        }
    }

    /**
     * Add pulse animation to element
     */
    pulse(element, duration = 2000) {
        if (element) {
            element.classList.add('pulse');
            setTimeout(() => {
                element.classList.remove('pulse');
            }, duration);
        }
    }
}

// Create global instance
window.loadingManager = new LoadingManager();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LoadingManager;
}

console.log('✅ LoadingManager module loaded');
