/**
 * DUAL REPORTING MANAGER
 * Manages the dual reporting system with toggle between Pre-reporting and Smart Reporter
 */

class DualReportingManager {
    constructor() {
        this.currentSide = 'pre-reporting'; // 'pre-reporting' or 'smart-reporter'
        this.isInitialized = false;
        this.preReportingData = null;
        this.businessRules = [];
        this.sharedConfig = {
            generatedBy: '',
            designation: ''
        };
        
        console.log('🎯 DualReportingManager initialized');
    }

    /**
     * Initialize the dual reporting system
     */
    async initialize() {
        if (this.isInitialized) {
            console.log('⚠️ DualReportingManager already initialized');
            return;
        }

        try {
            console.log('🚀 Initializing Dual Reporting System...');
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Load initial configuration
            await this.loadSharedConfiguration();
            
            // Load business rules
            await this.loadBusinessRules();
            
            // Initialize pre-reporting side (default)
            await this.initializePreReportingSide();
            
            // Hide loading spinner
            this.hideLoadingSpinner();
            
            this.isInitialized = true;
            console.log('✅ Dual Reporting System initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize Dual Reporting System:', error);
            this.showError('Failed to initialize reporting system: ' + error.message);
        }
    }

    /**
     * Setup event listeners for the dual reporting interface
     */
    setupEventListeners() {
        // Toggle switch
        const toggleInput = document.getElementById('reporting-toggle');
        if (toggleInput) {
            toggleInput.addEventListener('change', (e) => {
                this.handleToggleSwitch(e.target.checked);
            });
        }

        // Toggle options (clickable)
        const preReportingOption = document.getElementById('toggle-pre-reporting');
        const smartReporterOption = document.getElementById('toggle-smart-reporter');
        
        if (preReportingOption) {
            preReportingOption.addEventListener('click', () => {
                this.switchToSide('pre-reporting');
            });
        }
        
        if (smartReporterOption) {
            smartReporterOption.addEventListener('click', () => {
                this.switchToSide('smart-reporter');
            });
        }

        // Shared configuration inputs
        const generatedByInput = document.getElementById('dual-generated-by');
        const designationInput = document.getElementById('dual-designation');
        
        if (generatedByInput) {
            generatedByInput.addEventListener('input', (e) => {
                this.updateSharedConfig('generatedBy', e.target.value);
            });
        }
        
        if (designationInput) {
            designationInput.addEventListener('input', (e) => {
                this.updateSharedConfig('designation', e.target.value);
            });
        }

        // Pre-reporting controls
        this.setupPreReportingControls();
        
        // Smart reporter controls
        this.setupSmartReporterControls();

        console.log('✅ Event listeners setup complete');
    }

    /**
     * Setup pre-reporting side controls
     */
    setupPreReportingControls() {
        // Sorting controls
        const sortControls = [
            'sort-by-employees',
            'sort-by-change-flag', 
            'sort-by-priority',
            'sort-by-bulk-category'
        ];

        sortControls.forEach(controlId => {
            const control = document.getElementById(controlId);
            if (control) {
                control.addEventListener('change', (e) => {
                    this.handlePreReportingSorting(controlId, e.target.value);
                });
            }
        });

        // Generate Pre-Report button
        const generatePreReportBtn = document.getElementById('generate-pre-report-btn');
        if (generatePreReportBtn) {
            generatePreReportBtn.addEventListener('click', () => {
                this.generatePreReport();
            });
        }
    }

    /**
     * Setup smart reporter side controls
     */
    setupSmartReporterControls() {
        // Report type selection
        const reportTypeSelect = document.getElementById('report-type-selection');
        if (reportTypeSelect) {
            reportTypeSelect.addEventListener('change', (e) => {
                this.handleReportTypeChange(e.target.value);
            });
        }

        // Report format selection
        const reportFormatSelect = document.getElementById('report-format-selection');
        if (reportFormatSelect) {
            reportFormatSelect.addEventListener('change', (e) => {
                this.handleReportFormatChange(e.target.value);
            });
        }

        // Business rules buttons
        const addRuleBtn = document.getElementById('add-business-rule-btn');
        const manageRulesBtn = document.getElementById('manage-rules-btn');
        
        if (addRuleBtn) {
            addRuleBtn.addEventListener('click', () => {
                this.showAddBusinessRuleDialog();
            });
        }
        
        if (manageRulesBtn) {
            manageRulesBtn.addEventListener('click', () => {
                this.showManageRulesDialog();
            });
        }

        // Generate Final Report button
        const generateFinalReportBtn = document.getElementById('generate-final-report-btn');
        if (generateFinalReportBtn) {
            generateFinalReportBtn.addEventListener('click', () => {
                this.generateFinalReport();
            });
        }
    }

    /**
     * Handle toggle switch change
     */
    handleToggleSwitch(isSmartReporter) {
        const targetSide = isSmartReporter ? 'smart-reporter' : 'pre-reporting';
        this.switchToSide(targetSide);
    }

    /**
     * Switch between reporting sides
     */
    switchToSide(side) {
        if (this.currentSide === side) return;

        console.log(`🔄 Switching to ${side} side`);

        // Add visual feedback for toggle
        const currentOption = document.getElementById(`toggle-${this.currentSide}`);
        const targetOption = document.getElementById(`toggle-${side}`);

        if (window.visualFeedbackManager && targetOption) {
            window.visualFeedbackManager.pulse(targetOption);
        }

        // Update toggle state with animation
        const toggleInput = document.getElementById('reporting-toggle');
        if (toggleInput) {
            toggleInput.checked = (side === 'smart-reporter');
        }

        // Update toggle options with smooth transitions
        const preOption = document.getElementById('toggle-pre-reporting');
        const smartOption = document.getElementById('toggle-smart-reporter');

        if (preOption && smartOption) {
            preOption.classList.toggle('active', side === 'pre-reporting');
            smartOption.classList.toggle('active', side === 'smart-reporter');

            // Add transition effects
            if (window.transitionManager) {
                if (side === 'pre-reporting') {
                    window.transitionManager.bounce(preOption);
                } else {
                    window.transitionManager.bounce(smartOption);
                }
            }
        }

        // Get reporting sides for transition
        const preReportingSide = document.getElementById('pre-reporting-side');
        const smartReporterSide = document.getElementById('smart-reporter-side');
        const currentSideElement = document.getElementById(`${this.currentSide}-side`);
        const targetSideElement = document.getElementById(`${side}-side`);

        // Use transition manager for smooth side switching
        if (window.transitionManager && currentSideElement && targetSideElement) {
            window.transitionManager.transitionSidePanel(currentSideElement, targetSideElement);
        } else {
            // Fallback to CSS class toggle
            if (preReportingSide && smartReporterSide) {
                preReportingSide.classList.toggle('active', side === 'pre-reporting');
                smartReporterSide.classList.toggle('active', side === 'smart-reporter');
            }
        }

        this.currentSide = side;

        // Initialize the selected side with delay for smooth transition
        setTimeout(() => {
            if (side === 'smart-reporter') {
                this.initializeSmartReporterSide();
            } else {
                this.refreshPreReportingSide();
            }

            // Add staggered animation to content
            const targetContent = targetSideElement?.querySelector('.panel-content');
            if (window.transitionManager && targetContent) {
                window.transitionManager.animateStaggered(targetContent);
            }
        }, 200);

        // Show feedback toast
        if (window.loadingManager) {
            window.loadingManager.showToast({
                type: 'info',
                title: 'System Switched',
                message: `Now using ${side.replace('-', ' ').toUpperCase()} system`,
                duration: 2000
            });
        }

        console.log(`✅ Switched to ${side} side with smooth transitions`);
    }

    /**
     * Load shared configuration
     */
    async loadSharedConfiguration() {
        try {
            // Try to get configuration from existing payroll audit settings
            const signatureName = document.getElementById('signature-name')?.value || 'SAMUEL ASIEDU';
            const signatureDesignation = document.getElementById('signature-designation')?.value || 'PAYROLL AUDITOR';
            
            this.sharedConfig.generatedBy = signatureName;
            this.sharedConfig.designation = signatureDesignation;
            
            // Update the dual config inputs
            const generatedByInput = document.getElementById('dual-generated-by');
            const designationInput = document.getElementById('dual-designation');
            
            if (generatedByInput) generatedByInput.value = this.sharedConfig.generatedBy;
            if (designationInput) designationInput.value = this.sharedConfig.designation;
            
            console.log('✅ Shared configuration loaded:', this.sharedConfig);
            
        } catch (error) {
            console.error('❌ Failed to load shared configuration:', error);
        }
    }

    /**
     * Update shared configuration
     */
    updateSharedConfig(key, value) {
        this.sharedConfig[key] = value;
        console.log(`📝 Updated shared config ${key}:`, value);
        
        // Sync with main payroll audit configuration if exists
        if (key === 'generatedBy') {
            const signatureNameInput = document.getElementById('signature-name');
            if (signatureNameInput) signatureNameInput.value = value;
        } else if (key === 'designation') {
            const signatureDesignationInput = document.getElementById('signature-designation');
            if (signatureDesignationInput) signatureDesignationInput.value = value;
        }
    }

    /**
     * Load business rules
     */
    async loadBusinessRules() {
        try {
            // Load default business rules
            this.businessRules = [
                {
                    id: 'promotion-staff',
                    name: 'Staff Promotion Detection',
                    type: 'PROMOTION',
                    description: 'Basic salary increase + Job Title change',
                    isActive: true
                },
                {
                    id: 'promotion-minister',
                    name: 'Minister Promotion Detection', 
                    type: 'PROMOTION',
                    description: 'Job title change for Ministers department',
                    isActive: true
                },
                {
                    id: 'transfer-detection',
                    name: 'Transfer Detection',
                    type: 'TRANSFER', 
                    description: 'Change in Department',
                    isActive: true
                },
                {
                    id: 'loan-reporting',
                    name: 'Loan Reporting Rules',
                    type: 'LOAN',
                    description: 'Balance B/F and Current Deduction changes',
                    isActive: true
                }
            ];
            
            this.renderBusinessRules();
            console.log('✅ Business rules loaded:', this.businessRules.length);
            
        } catch (error) {
            console.error('❌ Failed to load business rules:', error);
        }
    }

    /**
     * Render business rules list
     */
    renderBusinessRules() {
        const rulesList = document.getElementById('business-rules-list');
        if (!rulesList) return;

        if (this.businessRules.length === 0) {
            rulesList.innerHTML = '<p class="no-rules">No business rules configured. Click "Add Rule" to create your first rule.</p>';
            return;
        }

        const rulesHtml = this.businessRules.map(rule => `
            <div class="business-rule-item ${rule.isActive ? 'active' : 'inactive'}" data-rule-id="${rule.id}">
                <div class="rule-info">
                    <div class="rule-name">${rule.name}</div>
                    <div class="rule-description">${rule.description}</div>
                    <div class="rule-type">${rule.type}</div>
                </div>
                <div class="rule-actions">
                    <button class="btn-toggle-rule ${rule.isActive ? 'btn-disable' : 'btn-enable'}" 
                            onclick="window.dualReportingManager.toggleRule('${rule.id}')">
                        ${rule.isActive ? 'Disable' : 'Enable'}
                    </button>
                </div>
            </div>
        `).join('');

        rulesList.innerHTML = rulesHtml;
    }

    /**
     * Hide loading spinner
     */
    hideLoadingSpinner() {
        const spinner = document.getElementById('dual-reporting-loading-spinner');
        if (spinner) {
            spinner.style.display = 'none';
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        console.error('❌ Dual Reporting Error:', message);
        // You can implement a proper error display here
        alert('Dual Reporting Error: ' + message);
    }

    // Pre-reporting side implementation
    async initializePreReportingSide() {
        console.log('🔄 Initializing Pre-Reporting side...');

        try {
            // Initialize enhanced pre-reporting if not already done
            if (!window.preReportingEnhanced) {
                window.preReportingEnhanced = new PreReportingEnhanced();
            }

            // Initialize the enhanced pre-reporting system
            await window.preReportingEnhanced.initialize();

            console.log('✅ Pre-Reporting side initialized');

        } catch (error) {
            console.error('❌ Failed to initialize Pre-Reporting side:', error);
        }
    }

    async initializeSmartReporterSide() {
        console.log('🔄 Initializing Smart Reporter side...');

        try {
            // Initialize Smart Reporter if not already done
            if (!window.smartReporter) {
                window.smartReporter = new SmartReporter();
            }

            // Initialize the Smart Reporter system
            await window.smartReporter.initialize();

            console.log('✅ Smart Reporter side initialized');

        } catch (error) {
            console.error('❌ Failed to initialize Smart Reporter side:', error);
        }
    }

    refreshPreReportingSide() {
        console.log('🔄 Refreshing Pre-Reporting side...');

        if (window.preReportingEnhanced && window.preReportingEnhanced.isInitialized) {
            // Refresh the data and re-render
            window.preReportingEnhanced.applyFiltersAndSort();
            window.preReportingEnhanced.renderPreReportingData();
        }
    }

    handlePreReportingSorting(controlId, value) {
        console.log(`📊 Pre-reporting sorting: ${controlId} = ${value}`);
        // Will be implemented in next phase
    }

    handleReportTypeChange(reportType) {
        console.log(`📋 Report type changed: ${reportType}`);
        // Will be implemented in next phase
    }

    handleReportFormatChange(format) {
        console.log(`📄 Report format changed: ${format}`);
        // Will be implemented in next phase
    }

    showAddBusinessRuleDialog() {
        console.log('➕ Show add business rule dialog');
        // Will be implemented in next phase
    }

    showManageRulesDialog() {
        console.log('⚙️ Show manage rules dialog');
        // Will be implemented in next phase
    }

    toggleRule(ruleId) {
        const rule = this.businessRules.find(r => r.id === ruleId);
        if (rule) {
            rule.isActive = !rule.isActive;
            this.renderBusinessRules();
            console.log(`🔄 Toggled rule ${ruleId}: ${rule.isActive}`);
        }
    }

    async generatePreReport() {
        console.log('📊 Generating Pre-Report...');
        // Will be implemented in next phase
    }

    async generateFinalReport() {
        console.log('🎯 Generating Final Report...');
        // Will be implemented in next phase
    }
}

// Initialize global instance
window.dualReportingManager = new DualReportingManager();

// Auto-initialization disabled to prevent startup popup
// The dual reporting manager will be initialized manually when needed
console.log('ℹ️ DualReportingManager auto-initialization disabled to prevent startup popup');

console.log('✅ DualReportingManager module loaded');
