#!/usr/bin/env python3
"""
Check database schema to find the correct table names
"""

import sqlite3

def check_schema():
    """Check database schema"""
    
    db_path = './data/templar_payroll_auditor.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all table names
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("📋 Database Tables:")
        for table in tables:
            print(f"   • {table[0]}")
            
        # Check for promotion/transfer related tables
        promotion_tables = [t[0] for t in tables if 'promotion' in t[0].lower() or 'transfer' in t[0].lower() or 'detection' in t[0].lower()]
        
        if promotion_tables:
            print(f"\n🔍 Promotion/Transfer related tables:")
            for table in promotion_tables:
                print(f"   • {table}")
                
                # Get schema for this table
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                print(f"     Columns:")
                for col in columns:
                    print(f"       - {col[1]} ({col[2]})")
                print()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Failed to check schema: {str(e)}")

if __name__ == "__main__":
    check_schema()
