#!/usr/bin/env python3
"""
Generate a sample report to show current format
"""

import sys
import os
import sqlite3
from datetime import datetime

# Add core to path
sys.path.append('./core')

def generate_sample_report():
    """Generate a sample report"""
    
    print("🎯 GENERATING SAMPLE REPORT")
    print("=" * 50)
    
    try:
        from smart_report_backend import SmartReportBackend
        
        # Get a test session
        db_path = './data/templar_payroll_auditor.db'
        
        if not os.path.exists(db_path):
            print("❌ Database not found")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute('SELECT DISTINCT session_id FROM comparison_results LIMIT 1')
        session_result = cursor.fetchone()
        conn.close()
        
        if not session_result:
            print("❌ No session data available")
            return False
        
        session_id = session_result[0]
        print(f"📊 Using session: {session_id}")
        
        # Initialize backend
        backend = SmartReportBackend(db_path)
        
        # Generate employee-based WORD report
        print("\n1. 📄 GENERATING EMPLOYEE-BASED WORD REPORT:")
        
        request = {
            'sessionId': session_id,
            'reportType': 'employee-based',
            'format': 'word',
            'config': {
                'generatedBy': 'SAMPLE GENERATOR',
                'designation': 'SYSTEM TESTER'
            },
            'includePromotions': True,
            'includeTransfers': True
        }
        
        result = backend.generate_smart_report(request)
        
        if result.get('success'):
            filename = result.get('filename')
            file_size = result.get('fileSize', 0)
            print(f"   ✅ Generated: {filename}")
            print(f"   📊 Size: {file_size:,} bytes")
            print(f"   📁 Location: ./reports/{filename}")
            
            # Try to read and show structure
            try:
                from docx import Document
                doc_path = f"./reports/{filename}"
                if os.path.exists(doc_path):
                    doc = Document(doc_path)
                    print(f"\n   📋 DOCUMENT STRUCTURE:")
                    print(f"   • Paragraphs: {len(doc.paragraphs)}")
                    print(f"   • Tables: {len(doc.tables)}")
                    
                    # Show first few paragraphs
                    print(f"\n   📝 CONTENT PREVIEW:")
                    for i, para in enumerate(doc.paragraphs[:10]):
                        if para.text.strip():
                            print(f"   {i+1}. {para.text[:80]}...")
                            
            except Exception as e:
                print(f"   ⚠️ Could not read document structure: {e}")
        else:
            print(f"   ❌ Failed: {result.get('error')}")
            return False
        
        # Generate item-based EXCEL report
        print("\n2. 📊 GENERATING ITEM-BASED EXCEL REPORT:")
        
        request['reportType'] = 'item-based'
        request['format'] = 'excel'
        
        result = backend.generate_smart_report(request)
        
        if result.get('success'):
            filename = result.get('filename')
            file_size = result.get('fileSize', 0)
            print(f"   ✅ Generated: {filename}")
            print(f"   📊 Size: {file_size:,} bytes")
            print(f"   📁 Location: ./reports/{filename}")
            
            # Try to read and show structure
            try:
                import openpyxl
                excel_path = f"./reports/{filename}"
                if os.path.exists(excel_path):
                    wb = openpyxl.load_workbook(excel_path)
                    print(f"\n   📋 WORKBOOK STRUCTURE:")
                    print(f"   • Worksheets: {len(wb.worksheets)}")
                    for ws in wb.worksheets:
                        print(f"     - {ws.title}: {ws.max_row} rows, {ws.max_column} columns")
                        
            except Exception as e:
                print(f"   ⚠️ Could not read workbook structure: {e}")
        else:
            print(f"   ❌ Failed: {result.get('error')}")
            return False
        
        print("\n✅ SAMPLE REPORTS GENERATED SUCCESSFULLY!")
        return True
        
    except Exception as e:
        print(f"❌ Error generating sample report: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_report_format_comparison():
    """Show comparison with expected format"""
    
    print("\n🔍 REPORT FORMAT ANALYSIS")
    print("=" * 50)
    
    print("\n📋 CURRENT REPORT STRUCTURE:")
    print("1. Header Section:")
    print("   • Title: PAYROLL AUDIT REPORT: [MONTH YEAR]")
    print("   • Report Information Table")
    print("   • Executive Summary")
    
    print("\n2. Findings Section:")
    print("   • Employee-based: Grouped by employee")
    print("   • Item-based: Grouped by payroll item")
    
    print("\n3. Appendix (if applicable):")
    print("   • Promotions and Transfers")
    print("   • New/Removed Employees")
    
    print("\n🎨 FORMATTING SPECIFICATIONS:")
    print("• WORD Format:")
    print("  - Body Font: Cambria (Body), 14pt")
    print("  - Heading Font: Calibri (Headings), 26pt")
    print("  - Tables with borders")
    print("  - Professional layout")
    
    print("\n• EXCEL Format:")
    print("  - Font: Calibri, 11pt body / 14pt headings")
    print("  - Multiple worksheets")
    print("  - Summary sheet")
    print("  - Data sheets by category")

def main():
    """Main execution"""
    
    print("🚀 SAMPLE REPORT GENERATOR")
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # Change to correct directory
    if not os.path.exists('./core/smart_report_backend.py'):
        print("❌ Please run this script from the project root directory")
        return
    
    # Generate sample reports
    success = generate_sample_report()
    
    # Show format comparison
    show_report_format_comparison()
    
    if success:
        print("\n🎉 SAMPLE GENERATION COMPLETE!")
        print("Check the ./reports/ folder for generated sample files.")
        print("\nTo compare with your specification:")
        print("1. Open the generated WORD document")
        print("2. Check the formatting, fonts, and structure")
        print("3. Compare with your attached sample")
    else:
        print("\n❌ SAMPLE GENERATION FAILED")
        print("Please check the error messages above.")

if __name__ == "__main__":
    main()
