# DUAL REPORTING SYSTEM ARCHITECTURE

## Overview
This document outlines the architectural design for the enhanced Pre-reporting UI that supports dual reporting functionality with toggle between traditional Pre-reporting and Smart Reporter.

## System Architecture

### 1. UI Structure
```
Pre-reporting Interface
├── Dual Report Configuration (Shared)
│   ├── Generated By (Input)
│   └── Designation (Input)
├── Toggle Switch (Pre-reporting ↔ Smart Reporter)
├── Pre-reporting Side (Traditional)
│   ├── Sorting Dropdowns
│   │   ├── Employees
│   │   ├── Change Flag (INCREASE, DECREASE, REMOVED, NEW, NO_CHANGE)
│   │   ├── Priority (High, Moderate, Low)
│   │   └── Bulk Category (Individual, Small, Medium, Large)
│   ├── Data Display (Interactive Table/Cards)
│   └── Generate-PRE-REPORT Button
└── Smart Reporter Side (New)
    ├── Report Type Dropdown (Employee-Based, Item-Based)
    ├── Business Rules Pane
    │   ├── Add Rule Button
    │   ├── Remove Rule Button
    │   └── Rules List
    ├── Format Selection (WORD, PDF, EXCEL)
    └── Generate-FINAL REPORT Button
```

### 2. Database Schema Extensions

#### New Tables Required:
```sql
-- Business Rules Storage
CREATE TABLE business_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name TEXT NOT NULL,
    rule_type TEXT CHECK(rule_type IN ('PROMOTION', 'TRANSFER', 'LOAN', 'GENERAL')),
    rule_condition TEXT NOT NULL, -- JSON condition
    rule_action TEXT NOT NULL,    -- JSON action
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Report Generation History
CREATE TABLE report_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,
    report_type TEXT CHECK(report_type IN ('PRE_REPORT', 'EMPLOYEE_BASED', 'ITEM_BASED')),
    format TEXT CHECK(format IN ('WORD', 'PDF', 'EXCEL')),
    generated_by TEXT,
    designation TEXT,
    file_path TEXT,
    generation_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES sessions(session_id)
);

-- Priority and Bulk Classification
CREATE TABLE change_classification (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,
    change_id INTEGER NOT NULL,
    priority_level TEXT CHECK(priority_level IN ('High', 'Moderate', 'Low')),
    bulk_category TEXT CHECK(bulk_category IN ('Individual', 'Small', 'Medium', 'Large')),
    bulk_size INTEGER DEFAULT 1,
    classification_reason TEXT,
    FOREIGN KEY (session_id) REFERENCES sessions(session_id),
    FOREIGN KEY (change_id) REFERENCES comparison_results(id)
);
```

### 3. Component Architecture

#### A. DualReportingManager (Main Controller)
- Manages toggle between Pre-reporting and Smart Reporter
- Handles shared configuration
- Coordinates data flow between components

#### B. PreReportingEnhanced (Traditional Side)
- Extends current pre-reporting functionality
- Adds sorting and filtering capabilities
- Maintains backward compatibility

#### C. SmartReporter (New Side)
- Implements AI-powered report generation
- Manages business rules engine
- Handles multiple report formats

#### D. BusinessRulesEngine
- Processes promotion detection rules
- Handles transfer identification
- Manages loan reporting logic
- Enforces strict rule compliance

#### E. ReportFormatManager
- Generates WORD reports with Cambria font
- Creates PDF reports with proper formatting
- Produces EXCEL reports with separate rules
- Manages template system

### 4. Data Flow Architecture

```
Current Pre-reporting Data
         ↓
DualReportingManager
         ↓
┌─────────────────┬─────────────────┐
│  Pre-reporting  │  Smart Reporter │
│     (Left)      │     (Right)     │
├─────────────────┼─────────────────┤
│ • Sort by       │ • Report Type   │
│   - Employees   │   Selection     │
│   - Change Flag │ • Business      │
│   - Priority    │   Rules Engine  │
│   - Bulk Cat.   │ • Format        │
│ • Generate      │   Selection     │
│   PRE-REPORT    │ • Generate      │
│                 │   FINAL REPORT  │
└─────────────────┴─────────────────┘
         ↓
    Report Manager
```

### 5. File Structure

```
ui/
├── dual_reporting_manager.js      (Main controller)
├── pre_reporting_enhanced.js      (Enhanced traditional side)
├── smart_reporter.js              (New smart reporting side)
├── business_rules_engine.js       (Rules processing)
├── report_format_manager.js       (Format generation)
└── dual_reporting_styles.css      (Styling)

core/
├── dual_reporting_backend.py      (Backend processing)
├── smart_report_generator.py      (AI-powered generation)
├── business_rules_processor.py    (Rules engine backend)
└── report_template_manager.py     (Template handling)
```

### 6. Integration Points

#### With Existing System:
- Uses current `comparison_results` table as data source
- Integrates with existing `sessions` management
- Maintains compatibility with current report manager
- Preserves all existing pre-reporting functionality

#### New Integrations:
- Business rules engine for intelligent reporting
- Priority classification system
- Bulk change categorization
- Multi-format report generation

### 7. Implementation Phases

1. **Phase 1**: Create dual UI structure with toggle
2. **Phase 2**: Implement shared configuration component
3. **Phase 3**: Enhance pre-reporting side with sorting
4. **Phase 4**: Build Smart Reporter framework
5. **Phase 5**: Implement business rules engine
6. **Phase 6**: Create report format managers
7. **Phase 7**: Add promotion/transfer detection
8. **Phase 8**: Integrate with report manager
9. **Phase 9**: Testing and validation
10. **Phase 10**: Final integration and deployment

This architecture ensures:
- ✅ No breaking changes to existing functionality
- ✅ Seamless integration with current database
- ✅ Professional implementation with task-based development
- ✅ Comprehensive testing at each phase
- ✅ Scalable and maintainable code structure
