#!/usr/bin/env python3
"""
Database Migration: Add Dual Reporting System Tables
Creates the necessary database tables for the dual reporting system functionality.
"""

import sqlite3
import os
import sys
from datetime import datetime

def create_dual_reporting_tables():
    """Create all tables required for the dual reporting system"""
    
    # Database path
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return False
    
    try:
        print("🚀 DUAL REPORTING SYSTEM - DATABASE MIGRATION")
        print("=" * 60)
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Business Rules Table
        print("\n1. 🏗️ Creating business_rules table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                rule_name TEXT NOT NULL,
                rule_type TEXT CHECK(rule_type IN ('PROMOTION', 'TRANSFER', 'LOAN', 'GENERAL')) NOT NULL,
                rule_condition TEXT NOT NULL, -- JSON condition
                rule_action TEXT NOT NULL,    -- JSON action
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("   ✅ Business rules table created")
        
        # 2. Report Generation History Table
        print("\n2. 🏗️ Creating report_history table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS report_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                report_type TEXT CHECK(report_type IN ('PRE_REPORT', 'EMPLOYEE_BASED', 'ITEM_BASED')) NOT NULL,
                format TEXT CHECK(format IN ('WORD', 'PDF', 'EXCEL')) NOT NULL,
                generated_by TEXT,
                designation TEXT,
                file_path TEXT,
                file_size INTEGER,
                generation_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'completed',
                error_message TEXT,
                FOREIGN KEY (session_id) REFERENCES sessions(session_id)
            )
        """)
        print("   ✅ Report history table created")
        
        # 3. Change Classification Table
        print("\n3. 🏗️ Creating change_classification table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS change_classification (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                change_id INTEGER NOT NULL,
                priority_level TEXT CHECK(priority_level IN ('High', 'Moderate', 'Low')) DEFAULT 'Moderate',
                bulk_category TEXT CHECK(bulk_category IN ('Individual', 'Small', 'Medium', 'Large')) DEFAULT 'Individual',
                bulk_size INTEGER DEFAULT 1,
                classification_reason TEXT,
                auto_classified BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES sessions(session_id),
                FOREIGN KEY (change_id) REFERENCES comparison_results(id)
            )
        """)
        print("   ✅ Change classification table created")
        
        # 4. Promotion and Transfer Detection Table
        print("\n4. 🏗️ Creating promotion_transfer_detection table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS promotion_transfer_detection (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                employee_id TEXT NOT NULL,
                employee_name TEXT,
                detection_type TEXT CHECK(detection_type IN ('PROMOTION_STAFF', 'PROMOTION_MINISTER', 'TRANSFER_STAFF', 'TRANSFER_MINISTER')) NOT NULL,
                previous_value TEXT,
                current_value TEXT,
                detection_confidence REAL DEFAULT 1.0,
                rule_applied TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES sessions(session_id)
            )
        """)
        print("   ✅ Promotion/Transfer detection table created")
        
        # 5. Smart Report Configuration Table
        print("\n5. 🏗️ Creating smart_report_config table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS smart_report_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                report_type TEXT CHECK(report_type IN ('employee-based', 'item-based')) DEFAULT 'employee-based',
                report_format TEXT CHECK(report_format IN ('word', 'pdf', 'excel')) DEFAULT 'word',
                generated_by TEXT,
                designation TEXT,
                include_promotions BOOLEAN DEFAULT 1,
                include_transfers BOOLEAN DEFAULT 1,
                include_loan_changes BOOLEAN DEFAULT 1,
                font_family TEXT DEFAULT 'Cambria (Body)',
                font_size INTEGER DEFAULT 14,
                heading_font TEXT DEFAULT 'Calibri (Headings)',
                heading_size INTEGER DEFAULT 26,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES sessions(session_id)
            )
        """)
        print("   ✅ Smart report configuration table created")
        
        # 6. Insert Default Business Rules
        print("\n6. 📝 Inserting default business rules...")
        
        default_rules = [
            {
                'name': 'Staff Promotion Detection',
                'type': 'PROMOTION',
                'condition': '{"basic_salary_increase": true, "job_title_change": true, "department_not_ministers": true}',
                'action': '{"classify_as": "PROMOTION_STAFF", "include_in_appendix": true}'
            },
            {
                'name': 'Minister Promotion Detection',
                'type': 'PROMOTION', 
                'condition': '{"job_title_change": true, "department_contains_ministers": true}',
                'action': '{"classify_as": "PROMOTION_MINISTER", "include_in_appendix": true}'
            },
            {
                'name': 'Staff Transfer Detection',
                'type': 'TRANSFER',
                'condition': '{"department_change": true, "department_not_ministers": true}',
                'action': '{"classify_as": "TRANSFER_STAFF", "include_in_appendix": true}'
            },
            {
                'name': 'Minister Transfer Detection',
                'type': 'TRANSFER',
                'condition': '{"department_change": true, "department_contains_ministers": true}',
                'action': '{"classify_as": "TRANSFER_MINISTER", "include_in_appendix": true}'
            },
            {
                'name': 'Loan Balance B/F Increase',
                'type': 'LOAN',
                'condition': '{"item_type": "loan", "balance_bf_increase": true}',
                'action': '{"report_format": "LOAN_TYPE Balance increased from PREV_VALUE to CURR_VALUE in MONTH YEAR"}'
            },
            {
                'name': 'Loan Current Deduction Increase',
                'type': 'LOAN',
                'condition': '{"item_type": "loan", "current_deduction_increase": true}',
                'action': '{"report_format": "LOAN_TYPE Current Deduction increased from PREV_VALUE to CURR_VALUE"}'
            }
        ]
        
        for rule in default_rules:
            cursor.execute("""
                INSERT OR IGNORE INTO business_rules (rule_name, rule_type, rule_condition, rule_action)
                VALUES (?, ?, ?, ?)
            """, (rule['name'], rule['type'], rule['condition'], rule['action']))
        
        print(f"   ✅ Inserted {len(default_rules)} default business rules")
        
        # 7. Create indexes for performance
        print("\n7. 🔍 Creating performance indexes...")
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_report_history_session ON report_history(session_id)",
            "CREATE INDEX IF NOT EXISTS idx_change_classification_session ON change_classification(session_id)",
            "CREATE INDEX IF NOT EXISTS idx_change_classification_change ON change_classification(change_id)",
            "CREATE INDEX IF NOT EXISTS idx_promotion_transfer_session ON promotion_transfer_detection(session_id)",
            "CREATE INDEX IF NOT EXISTS idx_promotion_transfer_employee ON promotion_transfer_detection(employee_id)",
            "CREATE INDEX IF NOT EXISTS idx_business_rules_type ON business_rules(rule_type)",
            "CREATE INDEX IF NOT EXISTS idx_business_rules_active ON business_rules(is_active)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        print(f"   ✅ Created {len(indexes)} performance indexes")
        
        # Commit all changes
        conn.commit()
        
        # 8. Verify table creation
        print("\n8. ✅ VERIFICATION:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND (name LIKE '%business%' OR name LIKE '%report%' OR name LIKE '%classification%' OR name LIKE '%promotion%')")
        tables = cursor.fetchall()

        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
                count = cursor.fetchone()[0]
                print(f"   📊 {table[0]}: {count} records")
            except Exception as e:
                print(f"   ⚠️ {table[0]}: Error counting records - {str(e)}")
        
        conn.close()
        
        print("\n🎉 DUAL REPORTING SYSTEM DATABASE MIGRATION COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"\n❌ Migration failed: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def verify_migration():
    """Verify that the migration was successful"""
    
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if all required tables exist
        required_tables = [
            'business_rules',
            'report_history', 
            'change_classification',
            'promotion_transfer_detection',
            'smart_report_config'
        ]
        
        print("\n🔍 VERIFYING MIGRATION:")
        all_exist = True
        
        for table in required_tables:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
            exists = cursor.fetchone() is not None
            status = "✅" if exists else "❌"
            print(f"   {status} {table}")
            if not exists:
                all_exist = False
        
        conn.close()
        
        if all_exist:
            print("\n✅ All dual reporting tables created successfully!")
        else:
            print("\n❌ Some tables are missing!")
            
        return all_exist
        
    except Exception as e:
        print(f"\n❌ Verification failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Dual Reporting System Database Migration...")
    
    # Create tables
    success = create_dual_reporting_tables()
    
    if success:
        # Verify migration
        verify_migration()
        print("\n🎯 Migration completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Migration failed!")
        sys.exit(1)
