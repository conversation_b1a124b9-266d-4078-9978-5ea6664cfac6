#!/usr/bin/env python3
"""
REPORT FORMAT MANAGER
Generates WORD, PDF, and EXCEL reports with proper formatting, fonts, and layout
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

# Import formatting libraries with fallbacks
try:
    from docx import Document
    from docx.shared import Inches, Pt
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.enum.table import WD_TABLE_ALIGNMENT
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

try:
    import openpyxl
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
    from openpyxl.utils import get_column_letter
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

class ReportFormatManager:
    """
    Manages report generation in multiple formats with proper styling
    """
    
    def __init__(self, output_dir: str = None):
        self.output_dir = output_dir or './reports'
        self.ensure_output_directory()
        
        # Font specifications from FINAL REPORT requirements
        self.fonts = {
            'word': {
                'body': 'Cambria (Body)',
                'heading': 'Calibri (Headings)',
                'body_size': 14,
                'heading_size': 26
            },
            'pdf': {
                'body': 'Times-Roman',
                'heading': 'Helvetica-Bold',
                'body_size': 12,
                'heading_size': 18
            },
            'excel': {
                'body': 'Calibri',
                'heading': 'Calibri',
                'body_size': 11,
                'heading_size': 14
            }
        }
    
    def ensure_output_directory(self):
        """Ensure output directory exists"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def generate_report(self, report_data: Dict[str, Any], format_type: str, 
                       filename: str = None) -> Dict[str, Any]:
        """
        Generate report in specified format
        
        Args:
            report_data: Report data structure
            format_type: 'word', 'pdf', or 'excel'
            filename: Optional custom filename
            
        Returns:
            Dict with success status and file path
        """
        try:
            print(f"🎯 Generating {format_type.upper()} report...")
            
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                report_type = report_data.get('metadata', {}).get('report_type', 'report')
                filename = f"payroll_audit_{report_type}_{timestamp}.{self._get_file_extension(format_type)}"
            
            file_path = os.path.join(self.output_dir, filename)
            
            # Generate based on format
            if format_type.lower() == 'word':
                result = self._generate_word_report(report_data, file_path)
            elif format_type.lower() == 'pdf':
                result = self._generate_pdf_report(report_data, file_path)
            elif format_type.lower() == 'excel':
                result = self._generate_excel_report(report_data, file_path)
            else:
                raise ValueError(f"Unsupported format: {format_type}")
            
            if result['success']:
                print(f"✅ {format_type.upper()} report generated: {file_path}")
            
            return result
            
        except Exception as e:
            print(f"❌ Failed to generate {format_type} report: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _get_file_extension(self, format_type: str) -> str:
        """Get file extension for format type"""
        extensions = {
            'word': 'docx',
            'pdf': 'pdf',
            'excel': 'xlsx'
        }
        return extensions.get(format_type.lower(), 'txt')
    
    def _generate_word_report(self, report_data: Dict[str, Any], file_path: str) -> Dict[str, Any]:
        """Generate WORD document with Cambria font"""
        
        if not DOCX_AVAILABLE:
            return {
                'success': False,
                'error': 'python-docx library not available. Install with: pip install python-docx'
            }
        
        try:
            doc = Document()
            
            # Set document fonts and styles
            self._setup_word_styles(doc)
            
            # Add header
            self._add_word_header(doc, report_data)

            # Add findings (no executive summary section)
            self._add_word_findings(doc, report_data)
            
            # Add appendix (new employees and removed employees)
            self._add_word_appendix(doc, report_data)

            # Add footer
            self._add_word_footer(doc, report_data)

            # Save document
            doc.save(file_path)
            
            return {
                'success': True,
                'file_path': file_path,
                'file_size': os.path.getsize(file_path)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Word generation failed: {str(e)}"
            }
    
    def _setup_word_styles(self, doc):
        """Setup Word document styles"""
        # This would set up custom styles for Cambria body and Calibri headings
        # For now, we'll use default styles and modify them
        pass
    
    def _add_word_header(self, doc, report_data: Dict[str, Any]):
        """Add header section to Word document matching exact specification"""
        report = report_data.get('report', {})
        header = report.get('header', {})
        info = header.get('report_information', {})
        exec_summary = header.get('executive_summary', {})

        # Title - centered and bold
        title_para = doc.add_paragraph()
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        title_run = title_para.add_run(header.get('title', 'PAYROLL AUDIT REPORT: JULY 2025'))
        title_run.font.name = 'Calibri'
        title_run.font.size = Pt(16)
        title_run.bold = True

        # Add spacing
        doc.add_paragraph()

        # Report Information and Executive Summary Table (exactly 4 rows, 2 columns)
        table = doc.add_table(rows=4, cols=2)
        table.style = 'Table Grid'

        # Set column widths (approximate)
        table.columns[0].width = Inches(3.0)
        table.columns[1].width = Inches(3.0)

        # Header row
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = 'Report Information'
        hdr_cells[1].text = 'Executive Summary'

        # Make header bold
        for cell in hdr_cells:
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    run.font.bold = True

        # Row 1: Period and Significant Changes
        row1_cells = table.rows[1].cells
        row1_cells[0].text = f"Period: {info.get('period', 'July 2025')}"
        row1_cells[1].text = f"Significant Changes Detected: {exec_summary.get('significant_changes', 4)}"

        # Row 2: Generated at and High Priority
        row2_cells = table.rows[2].cells
        row2_cells[0].text = f"Generated at: {info.get('generated_at', '2025-06-23 20:19:28')}"
        row2_cells[1].text = f"HIGH Priority Changes: {exec_summary.get('high_priority', 3)}"

        # Row 3: Generated By and MODERATE Priority
        row3_cells = table.rows[3].cells
        row3_cells[0].text = f"Generated By: {info.get('generated_by', 'Samuel Asiedu')}"
        row3_cells[1].text = f"MODERATE Priority Changes: {exec_summary.get('moderate_priority', 1)}"

        # Row 4: Designation and LOW Priority (this is the 4th row, index 3 already exists)
        # Actually, let me check the screenshot again - it shows exactly 4 rows total
        # So we need to put both Generated By + Designation in row 3, and MODERATE + LOW in row 3 right side
        row3_cells[0].text = f"Generated By: {info.get('generated_by', 'Samuel Asiedu')}\nDesignation: {info.get('designation', 'Sr. Audit Officer')}"
        row3_cells[1].text = f"MODERATE Priority Changes: {exec_summary.get('moderate_priority', 1)}\nLOW Priority Changes: {exec_summary.get('low_priority', 0)}"
    

    
    def _add_word_findings(self, doc, report_data: Dict[str, Any]):
        """Add findings section matching exact specification"""

        # Add spacing before findings (no page break - same page as header table)
        doc.add_paragraph()

        # Findings and Observations heading
        findings_heading = doc.add_heading('Finding and Observations', level=1)
        findings_heading.style.font.name = 'Calibri'
        findings_heading.style.font.size = Pt(14)
        findings_heading.style.font.bold = True

        report = report_data.get('report', {})

        if 'findings_and_observations' in report:
            self._add_word_employee_findings_exact_format(doc, report['findings_and_observations'])
        elif 'findings_by_item' in report:
            self._add_word_item_findings(doc, report['findings_by_item'])
    
    def _add_word_employee_findings_exact_format(self, doc, findings: Dict[str, Any]):
        """Add employee-based findings in exact specification format"""

        for emp_id, emp_data in findings.items():
            emp_info = emp_data.get('employee_info', {})
            changes = emp_data.get('changes', [])

            if not changes:  # Skip employees with no changes
                continue

            # Employee header with department - exact format
            emp_header = doc.add_paragraph()
            emp_header.style.font.name = 'Calibri'
            emp_header.style.font.size = Pt(11)
            emp_header.style.font.bold = True

            # Format: COP0209: APPIAH-AIDOO A – ABUAKWA AREA-MINISTERS
            emp_name = emp_info.get('name', 'Unknown').upper()
            emp_dept = emp_info.get('department', '').upper()

            # Only add department if it exists and is not empty
            if emp_dept and emp_dept.strip():
                emp_header.add_run(f"{emp_id}: {emp_name} – {emp_dept}")
            else:
                emp_header.add_run(f"{emp_id}: {emp_name} – UNKNOWN DEPARTMENT")

            # Add numbered changes list
            for i, change in enumerate(changes, 1):
                change_para = doc.add_paragraph()
                change_para.style.font.name = 'Calibri'
                change_para.style.font.size = Pt(11)

                # Format: "1. SCHOLARSHIP FUND increased from 5.00 to 20.00 in July 2025 (increase of 15.00)"
                item_name = change.get('item', 'Unknown Item').upper()
                change_type = change.get('change_type', 'changed').lower()
                prev_val = change.get('previous_value', '0.00')
                curr_val = change.get('current_value', '0.00')

                if change_type == 'increased':
                    diff = float(curr_val) - float(prev_val) if curr_val and prev_val else 0
                    change_text = f"{i}. {item_name} increased from {prev_val} to {curr_val} in July 2025 (increase of {diff:.2f})"
                elif change_type == 'decreased':
                    diff = float(prev_val) - float(curr_val) if curr_val and prev_val else 0
                    change_text = f"{i}. {item_name} decreased from {prev_val} to {curr_val} in July 2025 (decrease of {diff:.2f})"
                elif change_type == 'new':
                    change_text = f"{i}. {item_name} of {curr_val} was added to Payslip for July 2025."
                elif change_type == 'removed':
                    change_text = f"{i}. {item_name} of {prev_val} was removed from Payslip for July 2025."
                else:
                    change_text = f"{i}. {item_name} changed from {prev_val} to {curr_val} in July 2025."

                change_para.add_run(change_text)

            # Add spacing after each employee
            doc.add_paragraph()
    
    def _add_word_item_findings(self, doc, findings: Dict[str, Any]):
        """Add item-based findings"""
        for section_name, section_items in findings.items():
            doc.add_heading(section_name, level=2)
            
            for item_label, item_data in section_items.items():
                doc.add_heading(item_label, level=3)
                
                item_info = item_data.get('item_info', {})
                doc.add_paragraph(f"Total employees affected: {item_info.get('total_employees_affected', 0)}")
                
                for change_group in item_data.get('employee_changes', []):
                    doc.add_paragraph(f"{change_group['change_type']} ({change_group['employee_count']} employees):")
                    
                    for employee in change_group['employees'][:5]:  # Limit to first 5
                        doc.add_paragraph(f"  • {employee['employee_name']}: {employee['change_description']}")
    
    def _add_word_appendix(self, doc, report_data: Dict[str, Any]):
        """Add appendix section in exact specification format"""

        report = report_data.get('report', {})

        # PROMOTIONS section
        promotions = report.get('promotions', [])
        if promotions:
            doc.add_paragraph()

            # Separate promotions by staff and ministers
            staff_promotions = [p for p in promotions if p.get('type') == 'PROMOTION_STAFF']
            minister_promotions = [p for p in promotions if p.get('type') == 'PROMOTION_MINISTER']

            # STAFF PROMOTIONS
            if staff_promotions:
                # STAFF PROMOTIONS heading
                staff_promo_heading = doc.add_heading('STAFF PROMOTIONS', level=2)
                staff_promo_heading.style.font.name = 'Calibri'
                staff_promo_heading.style.font.size = Pt(12)
                staff_promo_heading.style.font.bold = True

                # Narrative text
                narrative_para = doc.add_paragraph()
                narrative_para.style.font.name = 'Calibri'
                narrative_para.style.font.size = Pt(11)
                narrative_para.add_run("The following Staff were promoted in July 2025:")

                # Employee list with bullets
                for promo in staff_promotions:
                    promo_para = doc.add_paragraph()
                    promo_para.style.font.name = 'Calibri'
                    promo_para.style.font.size = Pt(11)
                    promo_para.style = 'List Bullet'

                    # Format: COP1255: ABUAKWA AREA - STAFF - AMOS SIGNAAH ELIA ADAMS
                    emp_id = promo.get('employee_id', 'Unknown')
                    emp_name = promo.get('name', 'Unknown').upper()
                    emp_dept = promo.get('department', 'Unknown Department').upper()
                    promo_para.add_run(f"{emp_id}: {emp_dept} - {emp_name}")

            # MINISTER PROMOTIONS
            if minister_promotions:
                # MINISTER PROMOTIONS heading
                minister_promo_heading = doc.add_heading('MINISTER PROMOTIONS', level=2)
                minister_promo_heading.style.font.name = 'Calibri'
                minister_promo_heading.style.font.size = Pt(12)
                minister_promo_heading.style.font.bold = True

                # Narrative text
                narrative_para = doc.add_paragraph()
                narrative_para.style.font.name = 'Calibri'
                narrative_para.style.font.size = Pt(11)
                narrative_para.add_run("The following Ministers were promoted in July 2025:")

                # Employee list with bullets
                for promo in minister_promotions:
                    promo_para = doc.add_paragraph()
                    promo_para.style.font.name = 'Calibri'
                    promo_para.style.font.size = Pt(11)
                    promo_para.style = 'List Bullet'

                    # Format: COP1255: ABUAKWA AREA - MINISTERS - AMOS SIGNAAH ELIA ADAMS
                    emp_id = promo.get('employee_id', 'Unknown')
                    emp_name = promo.get('name', 'Unknown').upper()
                    emp_dept = promo.get('department', 'Unknown Department').upper()
                    promo_para.add_run(f"{emp_id}: {emp_dept} - {emp_name}")

        # TRANSFERS section
        transfers = report.get('transfers', [])
        if transfers:
            doc.add_paragraph()

            # Separate transfers by staff and ministers
            staff_transfers = [t for t in transfers if t.get('type') == 'TRANSFER_STAFF']
            minister_transfers = [t for t in transfers if t.get('type') == 'TRANSFER_MINISTER']

            # STAFF TRANSFERS
            if staff_transfers:
                # STAFF TRANSFERS heading
                staff_transfer_heading = doc.add_heading('STAFF TRANSFERS', level=2)
                staff_transfer_heading.style.font.name = 'Calibri'
                staff_transfer_heading.style.font.size = Pt(12)
                staff_transfer_heading.style.font.bold = True

                # Narrative text
                narrative_para = doc.add_paragraph()
                narrative_para.style.font.name = 'Calibri'
                narrative_para.style.font.size = Pt(11)
                narrative_para.add_run("The following Staff were transferred in July 2025:")

                # Employee list with bullets
                for transfer in staff_transfers:
                    transfer_para = doc.add_paragraph()
                    transfer_para.style.font.name = 'Calibri'
                    transfer_para.style.font.size = Pt(11)
                    transfer_para.style = 'List Bullet'

                    # Format: COP1255: ABUAKWA AREA - STAFF - AMOS SIGNAAH ELIA ADAMS
                    emp_id = transfer.get('employee_id', 'Unknown')
                    emp_name = transfer.get('name', 'Unknown').upper()
                    emp_dept = transfer.get('department', 'Unknown Department').upper()
                    transfer_para.add_run(f"{emp_id}: {emp_dept} - {emp_name}")

            # MINISTER TRANSFERS
            if minister_transfers:
                # MINISTER TRANSFERS heading
                minister_transfer_heading = doc.add_heading('MINISTER TRANSFERS', level=2)
                minister_transfer_heading.style.font.name = 'Calibri'
                minister_transfer_heading.style.font.size = Pt(12)
                minister_transfer_heading.style.font.bold = True

                # Narrative text
                narrative_para = doc.add_paragraph()
                narrative_para.style.font.name = 'Calibri'
                narrative_para.style.font.size = Pt(11)
                narrative_para.add_run("The following Ministers were transferred in July 2025:")

                # Employee list with bullets
                for transfer in minister_transfers:
                    transfer_para = doc.add_paragraph()
                    transfer_para.style.font.name = 'Calibri'
                    transfer_para.style.font.size = Pt(11)
                    transfer_para.style = 'List Bullet'

                    # Format: COP1255: ABUAKWA AREA - MINISTERS - AMOS SIGNAAH ELIA ADAMS
                    emp_id = transfer.get('employee_id', 'Unknown')
                    emp_name = transfer.get('name', 'Unknown').upper()
                    emp_dept = transfer.get('department', 'Unknown Department').upper()
                    transfer_para.add_run(f"{emp_id}: {emp_dept} - {emp_name}")

        # NEW EMPLOYEES section
        new_employees = report.get('new_employees', [])
        if new_employees:
            doc.add_paragraph()

            # NEW EMPLOYEES heading
            new_emp_heading = doc.add_heading('NEW EMPLOYEES', level=2)
            new_emp_heading.style.font.name = 'Calibri'
            new_emp_heading.style.font.size = Pt(12)
            new_emp_heading.style.font.bold = True

            # Narrative text
            narrative_para = doc.add_paragraph()
            narrative_para.style.font.name = 'Calibri'
            narrative_para.style.font.size = Pt(11)
            narrative_para.add_run("The following Employees were added to July 2025 Payroll:")

            # Employee list with bullets
            for emp in new_employees:
                emp_para = doc.add_paragraph()
                emp_para.style.font.name = 'Calibri'
                emp_para.style.font.size = Pt(11)
                emp_para.style = 'List Bullet'

                # Format: COP1255: ABUAKWA AREA - MINISTERS - AMOS SIGNAAH ELIA ADAMS
                emp_id = emp.get('employee_id', 'Unknown')
                emp_name = emp.get('name', 'Unknown').upper()
                emp_dept = emp.get('department', 'Unknown Department').upper()
                emp_para.add_run(f"{emp_id}: {emp_dept} - {emp_name}")

        # REMOVED EMPLOYEES section
        removed_employees = report.get('removed_employees', [])
        if removed_employees:
            doc.add_paragraph()

            # REMOVED EMPLOYEES heading
            removed_emp_heading = doc.add_heading('REMOVED EMPLOYEES', level=2)
            removed_emp_heading.style.font.name = 'Calibri'
            removed_emp_heading.style.font.size = Pt(12)
            removed_emp_heading.style.font.bold = True

            # Narrative text
            narrative_para = doc.add_paragraph()
            narrative_para.style.font.name = 'Calibri'
            narrative_para.style.font.size = Pt(11)
            narrative_para.add_run("The following Employees were removed to July 2025 Payroll:")

            # Employee list with bullets
            for emp in removed_employees:
                emp_para = doc.add_paragraph()
                emp_para.style.font.name = 'Calibri'
                emp_para.style.font.size = Pt(11)
                emp_para.style = 'List Bullet'

                # Format: COP1513: ABUAKWA AREA - MINISTERS - KPAKPOFIO ADOTEY
                emp_id = emp.get('employee_id', 'Unknown')
                emp_name = emp.get('name', 'Unknown').upper()
                emp_dept = emp.get('department', 'Unknown Department').upper()
                emp_para.add_run(f"{emp_id}: {emp_dept} - {emp_name}")

    def _add_word_footer(self, doc, report_data: Dict[str, Any]):
        """Add footer section in exact specification format"""

        # Add spacing before footer
        doc.add_paragraph()
        doc.add_paragraph()

        # Footer text - centered
        footer_para = doc.add_paragraph()
        footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        footer_para.style.font.name = 'Calibri'
        footer_para.style.font.size = Pt(10)

        # Format: Page 1 | TEMPLAR PAYROLL AUDITOR | All Rights Reserved © 2025
        footer_text = "Page 1 | TEMPLAR PAYROLL AUDITOR | All Rights Reserved © 2025"
        footer_para.add_run(footer_text)

    def _generate_pdf_report(self, report_data: Dict[str, Any], file_path: str) -> Dict[str, Any]:
        """Generate PDF document"""
        
        if not PDF_AVAILABLE:
            return {
                'success': False,
                'error': 'reportlab library not available. Install with: pip install reportlab'
            }
        
        try:
            # Create PDF document
            doc = SimpleDocTemplate(file_path, pagesize=A4)
            styles = getSampleStyleSheet()
            story = []
            
            # Add content
            self._add_pdf_content(story, styles, report_data)
            
            # Build PDF
            doc.build(story)
            
            return {
                'success': True,
                'file_path': file_path,
                'file_size': os.path.getsize(file_path)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"PDF generation failed: {str(e)}"
            }
    
    def _add_pdf_content(self, story, styles, report_data: Dict[str, Any]):
        """Add content to PDF story"""
        report = report_data.get('report', {})
        header = report.get('header', {})
        
        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Title'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # Center
        )
        
        story.append(Paragraph(header.get('title', 'PAYROLL AUDIT REPORT'), title_style))
        story.append(Spacer(1, 20))
        
        # Report Information
        info = header.get('report_information', {})
        story.append(Paragraph(f"<b>Period:</b> {info.get('period', 'N/A')}", styles['Normal']))
        story.append(Paragraph(f"<b>Generated By:</b> {info.get('generated_by', 'N/A')}", styles['Normal']))
        story.append(Paragraph(f"<b>Designation:</b> {info.get('designation', 'N/A')}", styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Executive Summary
        exec_summary = header.get('executive_summary', {})
        story.append(Paragraph('<b>Executive Summary</b>', styles['Heading2']))
        story.append(Paragraph(f"Significant Changes: {exec_summary.get('significant_changes_detected', 0)}", styles['Normal']))
        story.append(Paragraph(f"High Priority: {exec_summary.get('high_priority_changes', 0)}", styles['Normal']))
        
        # Add more content based on report type...
    
    def _generate_excel_report(self, report_data: Dict[str, Any], file_path: str) -> Dict[str, Any]:
        """Generate EXCEL spreadsheet"""
        
        if not EXCEL_AVAILABLE:
            return {
                'success': False,
                'error': 'openpyxl library not available. Install with: pip install openpyxl'
            }
        
        try:
            # Create workbook
            wb = openpyxl.Workbook()
            
            # Remove default sheet
            wb.remove(wb.active)
            
            # Add sheets based on report type
            report = report_data.get('report', {})
            
            if 'findings_and_observations' in report:
                self._add_excel_employee_sheets(wb, report)
            elif 'findings_by_item' in report:
                self._add_excel_item_sheets(wb, report)
            
            # Add summary sheet
            self._add_excel_summary_sheet(wb, report_data)
            
            # Save workbook
            wb.save(file_path)
            
            return {
                'success': True,
                'file_path': file_path,
                'file_size': os.path.getsize(file_path)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Excel generation failed: {str(e)}"
            }
    
    def _add_excel_employee_sheets(self, wb, report: Dict[str, Any]):
        """Add employee-based sheets to Excel"""
        
        # Employee Changes sheet
        ws = wb.create_sheet("Employee Changes")
        
        # Headers
        headers = ['Employee ID', 'Employee Name', 'Department', 'Section', 'Item', 'Description', 'Priority']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # Data
        row = 2
        findings = report.get('findings_and_observations', {})
        for emp_id, emp_data in findings.items():
            emp_info = emp_data.get('employee_info', {})
            
            for change in emp_data.get('changes', []):
                ws.cell(row=row, column=1, value=emp_info.get('id', ''))
                ws.cell(row=row, column=2, value=emp_info.get('name', ''))
                ws.cell(row=row, column=3, value=emp_info.get('department', ''))
                ws.cell(row=row, column=4, value=change.get('section', ''))
                ws.cell(row=row, column=5, value=change.get('item', ''))
                ws.cell(row=row, column=6, value=change.get('description', ''))
                ws.cell(row=row, column=7, value=change.get('priority', ''))
                row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _add_excel_item_sheets(self, wb, report: Dict[str, Any]):
        """Add item-based sheets to Excel"""
        
        # Item Changes sheet
        ws = wb.create_sheet("Item Changes")
        
        # Headers
        headers = ['Section', 'Item', 'Change Type', 'Employee Count', 'Employee ID', 'Employee Name', 'Description']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # Data
        row = 2
        findings = report.get('findings_by_item', {})
        for section_name, section_items in findings.items():
            for item_label, item_data in section_items.items():
                for change_group in item_data.get('employee_changes', []):
                    for employee in change_group.get('employees', []):
                        ws.cell(row=row, column=1, value=section_name)
                        ws.cell(row=row, column=2, value=item_label)
                        ws.cell(row=row, column=3, value=change_group.get('change_type', ''))
                        ws.cell(row=row, column=4, value=change_group.get('employee_count', 0))
                        ws.cell(row=row, column=5, value=employee.get('employee_id', ''))
                        ws.cell(row=row, column=6, value=employee.get('employee_name', ''))
                        ws.cell(row=row, column=7, value=employee.get('change_description', ''))
                        row += 1
    
    def _add_excel_summary_sheet(self, wb, report_data: Dict[str, Any]):
        """Add summary sheet to Excel"""
        
        ws = wb.create_sheet("Summary", 0)  # Insert at beginning
        
        # Title
        ws.cell(row=1, column=1, value="PAYROLL AUDIT REPORT SUMMARY").font = Font(size=16, bold=True)
        
        # Report Information
        report = report_data.get('report', {})
        header = report.get('header', {})
        info = header.get('report_information', {})
        
        ws.cell(row=3, column=1, value="Report Information").font = Font(bold=True)
        ws.cell(row=4, column=1, value="Period:")
        ws.cell(row=4, column=2, value=info.get('period', 'N/A'))
        ws.cell(row=5, column=1, value="Generated By:")
        ws.cell(row=5, column=2, value=info.get('generated_by', 'N/A'))
        ws.cell(row=6, column=1, value="Designation:")
        ws.cell(row=6, column=2, value=info.get('designation', 'N/A'))
        
        # Executive Summary
        exec_summary = header.get('executive_summary', {})
        ws.cell(row=8, column=1, value="Executive Summary").font = Font(bold=True)
        ws.cell(row=9, column=1, value="Total Changes:")
        ws.cell(row=9, column=2, value=exec_summary.get('significant_changes_detected', 0))
        ws.cell(row=10, column=1, value="High Priority:")
        ws.cell(row=10, column=2, value=exec_summary.get('high_priority_changes', 0))

def main():
    """Test the report format manager"""
    
    # Mock report data
    mock_data = {
        'report': {
            'header': {
                'title': 'PAYROLL AUDIT REPORT: JULY 2025',
                'report_information': {
                    'period': 'July 2025',
                    'generated_by': 'SAMUEL ASIEDU',
                    'designation': 'PAYROLL AUDITOR'
                },
                'executive_summary': {
                    'significant_changes_detected': 4,
                    'high_priority_changes': 3,
                    'moderate_priority_changes': 1,
                    'low_priority_changes': 0
                }
            }
        },
        'metadata': {
            'report_type': 'employee-based'
        }
    }
    
    manager = ReportFormatManager()
    
    # Test each format
    for format_type in ['word', 'pdf', 'excel']:
        result = manager.generate_report(mock_data, format_type)
        if result['success']:
            print(f"✅ {format_type.upper()} test passed")
        else:
            print(f"❌ {format_type.upper()} test failed: {result['error']}")

if __name__ == "__main__":
    main()
