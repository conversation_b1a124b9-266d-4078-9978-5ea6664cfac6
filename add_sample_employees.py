#!/usr/bin/env python3
"""
Add sample new and removed employees for testing the exact report format
"""

import sqlite3
import json
from datetime import datetime

def add_sample_employees():
    """Add sample new and removed employees to the database"""
    
    db_path = './data/templar_payroll_auditor.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get the latest session
        cursor.execute("SELECT DISTINCT session_id FROM comparison_results ORDER BY session_id DESC LIMIT 1")
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No sessions found")
            return
            
        session_id = session_result[0]
        print(f"📊 Adding sample employees to session: {session_id}")
        
        # Sample new employees data
        new_employees = [
            {
                'employee_id': 'COP1255',
                'employee_name': 'AMOS SIGNAAH ELIA ADAMS',
                'department': 'ABUAKWA AREA - MINISTERS',
                'job_title': 'MINISTER',
                'detection_type': 'NEW_EMPLOYEE'
            },
            {
                'employee_id': 'COP1425',
                'employee_name': 'ROBERT SAKYI-KAAH',
                'department': 'ABUAKWA AREA - STAFF',
                'job_title': 'CLERK',
                'detection_type': 'NEW_EMPLOYEE'
            },
            {
                'employee_id': 'COP1438',
                'employee_name': 'SAMUEL AMO BOATENG',
                'department': 'ABUAKWA AREA - MINISTERS',
                'job_title': 'MINISTER',
                'detection_type': 'NEW_EMPLOYEE'
            }
        ]
        
        # Sample removed employees data
        removed_employees = [
            {
                'employee_id': 'COP1513',
                'employee_name': 'KPAKPOFIO ADOTEY',
                'department': 'ABUAKWA AREA - MINISTERS',
                'job_title': 'MINISTER',
                'detection_type': 'REMOVED_EMPLOYEE'
            },
            {
                'employee_id': 'COP1658',
                'employee_name': 'ISAAC YVES SOWAH',
                'department': 'ABUAKWA AREA - MINISTERS',
                'job_title': 'MINISTER',
                'detection_type': 'REMOVED_EMPLOYEE'
            },
            {
                'employee_id': 'PW0271',
                'employee_name': 'MASLEY SETH GIDEON',
                'department': 'AFLAO AREA-WID/PENSIONS',
                'job_title': 'PENSIONER',
                'detection_type': 'REMOVED_EMPLOYEE'
            }
        ]
        
        # Insert into promotion_transfer_detection table
        insert_query = '''
            INSERT INTO promotion_transfer_detection
            (session_id, employee_id, employee_name, detection_type, previous_value, current_value, department, detection_data)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        '''
        
        # Insert new employees
        for emp in new_employees:
            details = json.dumps(emp)
            cursor.execute(insert_query, (
                session_id, emp['employee_id'], emp['employee_name'],
                emp['detection_type'], '', emp['job_title'], emp['department'], details
            ))
            
        # Insert removed employees
        for emp in removed_employees:
            details = json.dumps(emp)
            cursor.execute(insert_query, (
                session_id, emp['employee_id'], emp['employee_name'],
                emp['detection_type'], emp['job_title'], '', emp['department'], details
            ))
        
        conn.commit()
        conn.close()
        
        print(f"✅ Added {len(new_employees)} new employees and {len(removed_employees)} removed employees")
        print("📋 Sample employees added:")
        print("   NEW EMPLOYEES:")
        for emp in new_employees:
            print(f"   • {emp['employee_id']}: {emp['employee_name']} - {emp['department']}")
        print("   REMOVED EMPLOYEES:")
        for emp in removed_employees:
            print(f"   • {emp['employee_id']}: {emp['employee_name']} - {emp['department']}")
            
    except Exception as e:
        print(f"❌ Failed to add sample employees: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    add_sample_employees()
