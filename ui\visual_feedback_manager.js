/**
 * VISUAL FEEDBACK MANAGER
 * Handles interactive visual feedback, hover effects, and user interaction enhancements
 */

class VisualFeedbackManager {
    constructor() {
        this.scrollProgress = 0;
        this.tooltips = new Map();
        this.rippleElements = new Set();
        
        console.log('🎨 VisualFeedbackManager initialized');
        this.initialize();
    }

    /**
     * Initialize visual feedback systems
     */
    initialize() {
        this.setupScrollIndicator();
        this.setupRippleEffects();
        this.setupTooltips();
        this.setupSelectionFeedback();
        this.setupFormEnhancements();
        this.setupStatusIndicators();
        
        console.log('✅ Visual feedback systems initialized');
    }

    /**
     * Setup scroll progress indicator
     */
    setupScrollIndicator() {
        // Create scroll indicator
        const indicator = document.createElement('div');
        indicator.className = 'scroll-indicator';
        indicator.innerHTML = '<div class="scroll-progress" id="scroll-progress"></div>';
        document.body.appendChild(indicator);

        // Update scroll progress
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            
            const progressBar = document.getElementById('scroll-progress');
            if (progressBar) {
                progressBar.style.width = `${Math.min(100, Math.max(0, scrollPercent))}%`;
            }
        });

        console.log('📊 Scroll indicator setup complete');
    }

    /**
     * Setup ripple effects for buttons
     */
    setupRippleEffects() {
        // Add ripple effect to all buttons
        document.addEventListener('click', (e) => {
            // Ensure we have a valid DOM element before calling closest()
            if (e.target && e.target.nodeType === Node.ELEMENT_NODE && typeof e.target.closest === 'function') {
                const button = e.target.closest('button, .btn');
                if (button && !button.disabled) {
                    this.createRipple(button, e);
                }
            }
        });

        console.log('💫 Ripple effects setup complete');
    }

    /**
     * Create ripple effect
     */
    createRipple(element, event) {
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
            z-index: 1;
        `;

        // Add ripple animation keyframes if not exists
        if (!document.getElementById('ripple-styles')) {
            const style = document.createElement('style');
            style.id = 'ripple-styles';
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);

        // Remove ripple after animation
        setTimeout(() => {
            if (ripple.parentNode) {
                ripple.remove();
            }
        }, 600);
    }

    /**
     * Setup enhanced tooltips
     */
    setupTooltips() {
        // Enhanced tooltip handling with proper element checking
        document.addEventListener('mouseenter', (e) => {
            // Ensure we have a valid DOM element before calling closest()
            if (e.target && e.target.nodeType === Node.ELEMENT_NODE && typeof e.target.closest === 'function') {
                const element = e.target.closest('[data-tooltip]');
                if (element) {
                    this.showTooltip(element);
                }
            }
        }, true);

        document.addEventListener('mouseleave', (e) => {
            // Ensure we have a valid DOM element before calling closest()
            if (e.target && e.target.nodeType === Node.ELEMENT_NODE && typeof e.target.closest === 'function') {
                const element = e.target.closest('[data-tooltip]');
                if (element) {
                    this.hideTooltip(element);
                }
            }
        }, true);

        console.log('💬 Enhanced tooltips setup complete');
    }

    /**
     * Show enhanced tooltip
     */
    showTooltip(element) {
        const text = element.getAttribute('data-tooltip');
        if (!text) return;

        const tooltip = document.createElement('div');
        tooltip.className = 'enhanced-tooltip';
        tooltip.textContent = text;
        tooltip.style.cssText = `
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 10000;
            pointer-events: none;
            opacity: 0;
            transform: translateY(5px);
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        `;

        document.body.appendChild(tooltip);

        // Position tooltip
        const rect = element.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        
        let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
        let top = rect.top - tooltipRect.height - 8;

        // Adjust if tooltip goes off screen
        if (left < 8) left = 8;
        if (left + tooltipRect.width > window.innerWidth - 8) {
            left = window.innerWidth - tooltipRect.width - 8;
        }
        if (top < 8) {
            top = rect.bottom + 8;
        }

        tooltip.style.left = `${left}px`;
        tooltip.style.top = `${top}px`;

        // Show tooltip
        requestAnimationFrame(() => {
            tooltip.style.opacity = '1';
            tooltip.style.transform = 'translateY(0)';
        });

        this.tooltips.set(element, tooltip);
    }

    /**
     * Hide tooltip
     */
    hideTooltip(element) {
        const tooltip = this.tooltips.get(element);
        if (tooltip) {
            tooltip.style.opacity = '0';
            tooltip.style.transform = 'translateY(5px)';
            
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.remove();
                }
            }, 300);
            
            this.tooltips.delete(element);
        }
    }

    /**
     * Setup selection feedback
     */
    setupSelectionFeedback() {
        // Enhanced selection feedback
        document.addEventListener('click', (e) => {
            // Ensure we have a valid DOM element before calling closest()
            if (e.target && e.target.nodeType === Node.ELEMENT_NODE && typeof e.target.closest === 'function') {
                const selectable = e.target.closest('.selectable');
                if (selectable) {
                    this.toggleSelection(selectable);
                }
            }
        });

        console.log('✅ Selection feedback setup complete');
    }

    /**
     * Toggle selection state
     */
    toggleSelection(element) {
        const isSelected = element.classList.contains('selected');
        
        if (isSelected) {
            element.classList.remove('selected');
            this.animateDeselection(element);
        } else {
            element.classList.add('selected');
            this.animateSelection(element);
        }

        // Trigger custom event
        element.dispatchEvent(new CustomEvent('selectionChanged', {
            detail: { selected: !isSelected }
        }));
    }

    /**
     * Animate selection
     */
    animateSelection(element) {
        element.style.transform = 'scale(1.02)';
        setTimeout(() => {
            element.style.transform = '';
        }, 200);
    }

    /**
     * Animate deselection
     */
    animateDeselection(element) {
        element.style.transform = 'scale(0.98)';
        setTimeout(() => {
            element.style.transform = '';
        }, 200);
    }

    /**
     * Setup form enhancements
     */
    setupFormEnhancements() {
        // Enhanced focus effects
        document.addEventListener('focusin', (e) => {
            // Ensure we have a valid DOM element before calling closest()
            if (e.target && e.target.nodeType === Node.ELEMENT_NODE && typeof e.target.closest === 'function') {
                const formElement = e.target.closest('input, select, textarea');
                if (formElement) {
                    this.enhanceFocus(formElement);
                }
            }
        });

        document.addEventListener('focusout', (e) => {
            // Ensure we have a valid DOM element before calling closest()
            if (e.target && e.target.nodeType === Node.ELEMENT_NODE && typeof e.target.closest === 'function') {
                const formElement = e.target.closest('input, select, textarea');
                if (formElement) {
                    this.removeFocusEnhancement(formElement);
                }
            }
        });

        console.log('📝 Form enhancements setup complete');
    }

    /**
     * Enhance focus effect
     */
    enhanceFocus(element) {
        element.style.transform = 'scale(1.02)';
        element.style.transition = 'all 0.3s ease';
        
        // Add glow effect
        const glow = document.createElement('div');
        glow.className = 'focus-glow';
        glow.style.cssText = `
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #2196f3, #21cbf3);
            border-radius: inherit;
            z-index: -1;
            opacity: 0.3;
            filter: blur(4px);
            pointer-events: none;
        `;

        element.style.position = 'relative';
        element.appendChild(glow);
    }

    /**
     * Remove focus enhancement
     */
    removeFocusEnhancement(element) {
        element.style.transform = '';
        
        const glow = element.querySelector('.focus-glow');
        if (glow) {
            glow.remove();
        }
    }

    /**
     * Setup status indicators
     */
    setupStatusIndicators() {
        // Animate status indicators
        const indicators = document.querySelectorAll('.status-indicator');
        indicators.forEach(indicator => {
            this.animateStatusIndicator(indicator);
        });

        console.log('🔄 Status indicators setup complete');
    }

    /**
     * Animate status indicator
     */
    animateStatusIndicator(indicator) {
        if (indicator.classList.contains('active')) {
            indicator.style.animation = 'statusPulse 2s infinite';
        }
    }

    /**
     * Add hover effect to element
     */
    addHoverEffect(element, options = {}) {
        const {
            scale = 1.05,
            shadow = '0 6px 20px rgba(0, 0, 0, 0.1)',
            duration = '0.3s'
        } = options;

        element.style.transition = `all ${duration} ease`;
        
        element.addEventListener('mouseenter', () => {
            element.style.transform = `scale(${scale})`;
            element.style.boxShadow = shadow;
        });

        element.addEventListener('mouseleave', () => {
            element.style.transform = '';
            element.style.boxShadow = '';
        });
    }

    /**
     * Add pulse animation to element
     */
    pulse(element, duration = 1000) {
        element.classList.add('pulse');
        setTimeout(() => {
            element.classList.remove('pulse');
        }, duration);
    }

    /**
     * Add shake animation to element
     */
    shake(element) {
        element.style.animation = 'errorShake 0.6s ease-out';
        setTimeout(() => {
            element.style.animation = '';
        }, 600);
    }

    /**
     * Highlight element temporarily
     */
    highlight(element, color = '#2196f3', duration = 2000) {
        const originalBackground = element.style.backgroundColor;
        element.style.backgroundColor = color;
        element.style.transition = 'background-color 0.3s ease';
        
        setTimeout(() => {
            element.style.backgroundColor = originalBackground;
        }, duration);
    }

    /**
     * Add loading state to element
     */
    addLoadingState(element) {
        element.classList.add('loading-state');
        element.style.opacity = '0.7';
        element.style.pointerEvents = 'none';
    }

    /**
     * Remove loading state from element
     */
    removeLoadingState(element) {
        element.classList.remove('loading-state');
        element.style.opacity = '';
        element.style.pointerEvents = '';
    }
}

// Create global instance
window.visualFeedbackManager = new VisualFeedbackManager();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VisualFeedbackManager;
}

console.log('✅ VisualFeedbackManager module loaded');
