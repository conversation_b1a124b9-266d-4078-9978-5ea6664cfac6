#!/usr/bin/env python3
"""
DATABASE FIXES - MISSING COLUMNS
Adds missing 'department' column to promotion_transfer_detection table
and fixes any other column reference issues
"""

import os
import sys
import sqlite3
from datetime import datetime

def add_missing_columns():
    """Add missing columns to database tables"""
    
    print("🔧 FIXING DATABASE MISSING COLUMNS")
    print("=" * 50)
    
    db_path = './data/templar_payroll_auditor.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Add department column to promotion_transfer_detection
        print("\n1. 📊 ADDING DEPARTMENT COLUMN TO promotion_transfer_detection:")
        
        # Check if department column already exists
        cursor.execute("PRAGMA table_info(promotion_transfer_detection)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'department' not in columns:
            print("   Adding department column...")
            cursor.execute("""
                ALTER TABLE promotion_transfer_detection 
                ADD COLUMN department TEXT
            """)
            print("   ✅ Department column added successfully")
        else:
            print("   ✅ Department column already exists")
        
        # 2. Add detection_data column if missing (for storing detailed detection info)
        if 'detection_data' not in columns:
            print("   Adding detection_data column...")
            cursor.execute("""
                ALTER TABLE promotion_transfer_detection 
                ADD COLUMN detection_data TEXT
            """)
            print("   ✅ Detection_data column added successfully")
        else:
            print("   ✅ Detection_data column already exists")
        
        # 3. Check and fix comparison_results table if needed
        print("\n2. 📊 CHECKING comparison_results TABLE:")
        
        cursor.execute("PRAGMA table_info(comparison_results)")
        comp_columns = [col[1] for col in cursor.fetchall()]
        print(f"   Available columns: {comp_columns}")
        
        # Check if we need to add any missing columns
        expected_columns = ['numeric_difference', 'percentage_change']
        missing_comp_columns = [col for col in expected_columns if col not in comp_columns]
        
        for col in missing_comp_columns:
            print(f"   Adding missing column: {col}")
            if col == 'numeric_difference':
                cursor.execute("ALTER TABLE comparison_results ADD COLUMN numeric_difference REAL")
            elif col == 'percentage_change':
                cursor.execute("ALTER TABLE comparison_results ADD COLUMN percentage_change REAL")
            print(f"   ✅ {col} column added")
        
        if not missing_comp_columns:
            print("   ✅ All expected columns present")
        
        # 4. Update existing promotion_transfer_detection records with department info
        print("\n3. 🔄 UPDATING EXISTING RECORDS WITH DEPARTMENT INFO:")
        
        # Get records without department
        cursor.execute("""
            SELECT ptd.id, ptd.employee_id, ptd.session_id
            FROM promotion_transfer_detection ptd
            WHERE ptd.department IS NULL OR ptd.department = ''
        """)
        records_to_update = cursor.fetchall()
        
        print(f"   Found {len(records_to_update)} records needing department update")
        
        # Update each record with department from employees table
        updated_count = 0
        for record_id, employee_id, session_id in records_to_update:
            cursor.execute("""
                SELECT department FROM employees 
                WHERE employee_id = ? AND session_id = ?
                LIMIT 1
            """, (employee_id, session_id))
            
            dept_result = cursor.fetchone()
            if dept_result and dept_result[0]:
                cursor.execute("""
                    UPDATE promotion_transfer_detection 
                    SET department = ? 
                    WHERE id = ?
                """, (dept_result[0], record_id))
                updated_count += 1
        
        print(f"   ✅ Updated {updated_count} records with department information")
        
        # 5. Create indexes for better performance
        print("\n4. 📈 CREATING PERFORMANCE INDEXES:")
        
        indexes_to_create = [
            ("idx_ptd_session_employee", "promotion_transfer_detection", "session_id, employee_id"),
            ("idx_ptd_detection_type", "promotion_transfer_detection", "detection_type"),
            ("idx_ptd_department", "promotion_transfer_detection", "department"),
            ("idx_cc_session_priority", "change_classification", "session_id, priority_level"),
            ("idx_cc_bulk_category", "change_classification", "bulk_category")
        ]
        
        for index_name, table_name, columns in indexes_to_create:
            try:
                cursor.execute(f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} ({columns})")
                print(f"   ✅ Created index: {index_name}")
            except sqlite3.Error as e:
                print(f"   ⚠️ Index {index_name} already exists or error: {e}")
        
        # Commit all changes
        conn.commit()
        conn.close()
        
        print("\n✅ DATABASE FIXES COMPLETED SUCCESSFULLY")
        return True
        
    except Exception as e:
        print(f"\n❌ Database fixes failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def verify_fixes():
    """Verify that all fixes were applied correctly"""
    
    print("\n🔍 VERIFYING DATABASE FIXES")
    print("=" * 50)
    
    db_path = './data/templar_payroll_auditor.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Verify promotion_transfer_detection table
        print("\n1. 📊 VERIFYING promotion_transfer_detection TABLE:")
        
        cursor.execute("PRAGMA table_info(promotion_transfer_detection)")
        columns = [col[1] for col in cursor.fetchall()]
        
        required_columns = ['department', 'detection_data']
        for col in required_columns:
            if col in columns:
                print(f"   ✅ {col} column present")
            else:
                print(f"   ❌ {col} column missing")
                return False
        
        # Check if department data is populated
        cursor.execute("""
            SELECT COUNT(*) as total,
                   COUNT(CASE WHEN department IS NOT NULL AND department != '' THEN 1 END) as with_dept
            FROM promotion_transfer_detection
        """)
        total, with_dept = cursor.fetchone()
        
        if total > 0:
            print(f"   📊 Records: {total} total, {with_dept} with department ({(with_dept/total)*100:.1f}%)")
        else:
            print("   📊 No records in table (expected for new system)")
        
        # 2. Verify comparison_results table
        print("\n2. 📊 VERIFYING comparison_results TABLE:")
        
        cursor.execute("PRAGMA table_info(comparison_results)")
        comp_columns = [col[1] for col in cursor.fetchall()]
        
        optional_columns = ['numeric_difference', 'percentage_change']
        for col in optional_columns:
            if col in comp_columns:
                print(f"   ✅ {col} column present")
            else:
                print(f"   ⚠️ {col} column missing (optional)")
        
        # 3. Verify indexes
        print("\n3. 📈 VERIFYING INDEXES:")
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%'")
        indexes = [row[0] for row in cursor.fetchall()]
        
        expected_indexes = [
            'idx_ptd_session_employee',
            'idx_ptd_detection_type', 
            'idx_ptd_department',
            'idx_cc_session_priority',
            'idx_cc_bulk_category'
        ]
        
        for idx in expected_indexes:
            if idx in indexes:
                print(f"   ✅ {idx}")
            else:
                print(f"   ⚠️ {idx} missing")
        
        conn.close()
        
        print("\n✅ VERIFICATION COMPLETED")
        return True
        
    except Exception as e:
        print(f"\n❌ Verification failed: {str(e)}")
        return False

def test_detection_system():
    """Test that the detection system works with the fixes"""
    
    print("\n🧪 TESTING DETECTION SYSTEM WITH FIXES")
    print("=" * 50)
    
    try:
        # Test the promotion transfer detector
        sys.path.append('./core')
        from promotion_transfer_detector import PromotionTransferDetector
        
        db_path = './data/templar_payroll_auditor.db'
        detector = PromotionTransferDetector(db_path)
        
        # Get a test session
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT DISTINCT session_id FROM comparison_results LIMIT 1")
        session_result = cursor.fetchone()
        conn.close()
        
        if session_result:
            test_session = session_result[0]
            print(f"   Testing with session: {test_session}")
            
            # Test detection
            result = detector.detect_all_changes(test_session)
            
            if result.get('success'):
                print("   ✅ Detection system working correctly")
                
                # Check if department data is being saved
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT COUNT(*) FROM promotion_transfer_detection 
                    WHERE session_id = ? AND department IS NOT NULL
                """, (test_session,))
                dept_count = cursor.fetchone()[0]
                conn.close()
                
                print(f"   ✅ {dept_count} detection records have department data")
                return True
            else:
                print(f"   ❌ Detection failed: {result.get('error')}")
                return False
        else:
            print("   ⚠️ No sessions available for testing")
            return True  # Not a failure if no data exists
        
    except Exception as e:
        print(f"   ❌ Detection system test failed: {str(e)}")
        return False

def main():
    """Main execution function"""
    
    print("🚀 STARTING DATABASE FIXES")
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Change to correct directory
    if not os.path.exists('./data/templar_payroll_auditor.db'):
        print("❌ Please run this script from the project root directory")
        sys.exit(1)
    
    # Execute fixes
    step1_success = add_missing_columns()
    step2_success = verify_fixes()
    step3_success = test_detection_system()
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 DATABASE FIXES SUMMARY")
    print("=" * 70)
    
    print(f"Add Missing Columns: {'✅ SUCCESS' if step1_success else '❌ FAILED'}")
    print(f"Verify Fixes: {'✅ SUCCESS' if step2_success else '❌ FAILED'}")
    print(f"Test Detection System: {'✅ SUCCESS' if step3_success else '❌ FAILED'}")
    
    if step1_success and step2_success and step3_success:
        print("\n🎉 ALL DATABASE FIXES COMPLETED SUCCESSFULLY!")
        print("   The database is now ready for enhanced dual reporting operations.")
        sys.exit(0)
    else:
        print("\n⚠️ SOME FIXES FAILED - REVIEW REQUIRED")
        sys.exit(1)

if __name__ == "__main__":
    main()
