/**
 * ENHANCED PRE-REPORTING MODULE
 * Extends the existing pre-reporting functionality with advanced sorting and filtering
 */

class PreReportingEnhanced {
    constructor() {
        this.rawData = [];
        this.filteredData = [];
        this.selectedChanges = new Set();
        this.currentFilters = {
            employees: 'employee-no',
            changeFlag: 'all',
            priority: 'all',
            bulkCategory: 'all'
        };
        this.isInitialized = false;
        
        console.log('🎯 PreReportingEnhanced initialized');
    }

    /**
     * Initialize the enhanced pre-reporting system
     */
    async initialize() {
        if (this.isInitialized) {
            console.log('⚠️ PreReportingEnhanced already initialized');
            return;
        }

        try {
            console.log('🚀 Initializing Enhanced Pre-Reporting...');
            
            // Load pre-reporting data
            await this.loadPreReportingData();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Apply initial classification
            await this.classifyChanges();
            
            // Render the data
            this.renderPreReportingData();
            
            this.isInitialized = true;
            console.log('✅ Enhanced Pre-Reporting initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize Enhanced Pre-Reporting:', error);
            this.showError('Failed to initialize pre-reporting: ' + error.message);
        }
    }

    /**
     * Load pre-reporting data from the backend
     */
    async loadPreReportingData() {
        try {
            console.log('📊 Loading pre-reporting data...');

            // Show skeleton loading in data container
            const dataContainer = document.getElementById('pre-reporting-data');
            if (dataContainer && window.loadingManager) {
                window.loadingManager.showSkeleton(dataContainer, { lines: 5 });
            }

            // Use existing API to get latest pre-reporting data
            if (window.api && window.api.getLatestPreReportingData) {
                const response = await window.api.getLatestPreReportingData();

                if (response && response.length > 0) {
                    this.rawData = response;
                    console.log(`✅ Loaded ${this.rawData.length} pre-reporting records`);

                    // Don't show popup - just log the success
                    console.log(`ℹ️ Pre-reporting data loaded: ${this.rawData.length} records`);
                }
                } else {
                    console.log('⚠️ No pre-reporting data available');
                    this.rawData = [];

                    // Don't show popup - just log the status
                    console.log('ℹ️ Pre-reporting data not available - this is normal when no audit has been run');
                }
            } else {
                console.log('⚠️ API not available, using mock data');
                this.rawData = this.generateMockData();

                // Don't show popup - just log the status
                console.log('ℹ️ API not available - using mock data for demonstration');
            }

            // Initialize filtered data
            this.filteredData = [...this.rawData];

        } catch (error) {
            console.error('❌ Failed to load pre-reporting data:', error);
            // Fallback to mock data
            this.rawData = this.generateMockData();
            this.filteredData = [...this.rawData];

            // Don't show popup - just log the error
            console.log('ℹ️ Failed to load pre-reporting data, using sample data');
        }
    }

    /**
     * Generate mock data for testing
     */
    generateMockData() {
        return [
            {
                id: 1,
                employee_id: 'COP0209',
                employee_name: 'APPIAH-AIDOO A',
                section_name: 'EARNINGS',
                item_label: 'SCHOLARSHIP FUND',
                previous_value: '5.00',
                current_value: '20.00',
                change_type: 'INCREASED',
                priority: 'High',
                numeric_difference: 15.00,
                percentage_change: 300.0
            },
            {
                id: 2,
                employee_id: 'COP2626',
                employee_name: 'SAMUEL ASIEDU',
                section_name: 'DEDUCTIONS',
                item_label: 'RESPONSIBILITY HEADS',
                previous_value: '2000.00',
                current_value: '1500.00',
                change_type: 'DECREASED',
                priority: 'Moderate',
                numeric_difference: -500.00,
                percentage_change: -25.0
            },
            {
                id: 3,
                employee_id: 'COP2524',
                employee_name: 'MARK ANTHONY',
                section_name: 'ALLOWANCES',
                item_label: 'SECURITY GUARD ALLOWANCE',
                previous_value: '800.00',
                current_value: '0.00',
                change_type: 'REMOVED',
                priority: 'High',
                numeric_difference: -800.00,
                percentage_change: -100.0
            },
            {
                id: 4,
                employee_id: 'COP4040',
                employee_name: 'EBENEZER ARHIN',
                section_name: 'ALLOWANCES',
                item_label: 'NEW LAND ALLOWANCE',
                previous_value: '0.00',
                current_value: '700.00',
                change_type: 'NEW',
                priority: 'Moderate',
                numeric_difference: 700.00,
                percentage_change: 0
            }
        ];
    }

    /**
     * Setup event listeners for sorting controls
     */
    setupEventListeners() {
        const sortControls = [
            'sort-by-employees',
            'sort-by-change-flag',
            'sort-by-priority', 
            'sort-by-bulk-category'
        ];

        sortControls.forEach(controlId => {
            const control = document.getElementById(controlId);
            if (control) {
                control.addEventListener('change', (e) => {
                    this.handleSortingChange(controlId, e.target.value);
                });
            }
        });

        // Generate Pre-Report button
        const generateBtn = document.getElementById('generate-pre-report-btn');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => {
                this.generatePreReport();
            });
        }
    }

    /**
     * Handle sorting/filtering changes
     */
    handleSortingChange(controlId, value) {
        console.log(`📊 Sorting change: ${controlId} = ${value}`);
        
        // Update current filters
        switch (controlId) {
            case 'sort-by-employees':
                this.currentFilters.employees = value;
                break;
            case 'sort-by-change-flag':
                this.currentFilters.changeFlag = value;
                break;
            case 'sort-by-priority':
                this.currentFilters.priority = value;
                break;
            case 'sort-by-bulk-category':
                this.currentFilters.bulkCategory = value;
                break;
        }
        
        // Apply filters and re-render
        this.applyFiltersAndSort();
        this.renderPreReportingData();
    }

    /**
     * Apply filters and sorting to the data
     */
    applyFiltersAndSort() {
        let data = [...this.rawData];
        
        // Apply change flag filter
        if (this.currentFilters.changeFlag !== 'all') {
            data = data.filter(item => item.change_type === this.currentFilters.changeFlag);
        }
        
        // Apply priority filter
        if (this.currentFilters.priority !== 'all') {
            data = data.filter(item => item.priority === this.currentFilters.priority);
        }
        
        // Apply bulk category filter (placeholder - will be enhanced)
        if (this.currentFilters.bulkCategory !== 'all') {
            // This will be implemented when bulk classification is added
            console.log('🔄 Bulk category filtering not yet implemented');
        }
        
        // Apply sorting
        switch (this.currentFilters.employees) {
            case 'employee-no':
                data.sort((a, b) => a.employee_id.localeCompare(b.employee_id));
                break;
            case 'employee-name':
                data.sort((a, b) => a.employee_name.localeCompare(b.employee_name));
                break;
            case 'department':
                data.sort((a, b) => (a.department || '').localeCompare(b.department || ''));
                break;
        }
        
        this.filteredData = data;
        console.log(`📊 Filtered data: ${this.filteredData.length} records`);
    }

    /**
     * Classify changes for priority and bulk categorization
     */
    async classifyChanges() {
        console.log('🔍 Classifying changes...');
        
        this.rawData.forEach(change => {
            // Auto-classify priority if not set
            if (!change.priority) {
                change.priority = this.classifyPriority(change);
            }
            
            // Auto-classify bulk category
            change.bulk_category = this.classifyBulkCategory(change);
        });
        
        console.log('✅ Change classification completed');
    }

    /**
     * Classify priority level based on change characteristics
     */
    classifyPriority(change) {
        const absChange = Math.abs(change.numeric_difference || 0);
        const percentChange = Math.abs(change.percentage_change || 0);
        
        // High priority: Large amounts or high percentages
        if (absChange > 1000 || percentChange > 50 || change.change_type === 'NEW' || change.change_type === 'REMOVED') {
            return 'High';
        }
        
        // Low priority: Small changes
        if (absChange < 100 && percentChange < 10) {
            return 'Low';
        }
        
        // Default to moderate
        return 'Moderate';
    }

    /**
     * Classify bulk category
     */
    classifyBulkCategory(change) {
        // For now, classify as individual
        // This will be enhanced with actual bulk detection logic
        return 'individual';
    }

    /**
     * Render the pre-reporting data
     */
    renderPreReportingData() {
        const container = document.getElementById('pre-reporting-data-container');
        if (!container) {
            console.error('❌ Pre-reporting data container not found');
            return;
        }

        if (this.filteredData.length === 0) {
            container.innerHTML = `
                <div class="no-data-message">
                    <i class="fas fa-info-circle"></i>
                    <h3>No Changes Found</h3>
                    <p>No payroll changes match the current filter criteria.</p>
                </div>
            `;
            return;
        }

        // Group data by employee for better display
        const groupedData = this.groupDataByEmployee(this.filteredData);
        
        const html = `
            <div class="pre-reporting-summary">
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-number">${this.filteredData.length}</span>
                        <span class="stat-label">Total Changes</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${Object.keys(groupedData).length}</span>
                        <span class="stat-label">Employees</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${this.selectedChanges.size}</span>
                        <span class="stat-label">Selected</span>
                    </div>
                </div>
            </div>
            
            <div class="pre-reporting-data">
                ${Object.entries(groupedData).map(([employeeId, changes]) => 
                    this.renderEmployeeGroup(employeeId, changes)
                ).join('')}
            </div>
        `;
        
        container.innerHTML = html;
        
        // Attach event listeners for selection
        this.attachSelectionListeners();
    }

    /**
     * Group data by employee
     */
    groupDataByEmployee(data) {
        return data.reduce((groups, change) => {
            const key = change.employee_id;
            if (!groups[key]) {
                groups[key] = [];
            }
            groups[key].push(change);
            return groups;
        }, {});
    }

    /**
     * Render employee group
     */
    renderEmployeeGroup(employeeId, changes) {
        const employee = changes[0]; // Get employee info from first change
        
        return `
            <div class="employee-group" data-employee-id="${employeeId}">
                <div class="employee-header">
                    <div class="employee-info">
                        <h4>${employee.employee_name}</h4>
                        <span class="employee-id">${employee.employee_id}</span>
                    </div>
                    <div class="employee-stats">
                        <span class="change-count">${changes.length} changes</span>
                    </div>
                </div>
                <div class="employee-changes">
                    ${changes.map(change => this.renderChangeItem(change)).join('')}
                </div>
            </div>
        `;
    }

    /**
     * Render individual change item
     */
    renderChangeItem(change) {
        const isSelected = this.selectedChanges.has(change.id);
        const priorityClass = change.priority ? change.priority.toLowerCase() : 'moderate';
        const changeTypeClass = change.change_type ? change.change_type.toLowerCase() : '';
        
        return `
            <div class="change-item ${priorityClass} ${changeTypeClass} ${isSelected ? 'selected' : ''}" 
                 data-change-id="${change.id}">
                <div class="change-content">
                    <div class="change-main">
                        <div class="change-label">${change.item_label}</div>
                        <div class="change-section">${change.section_name}</div>
                    </div>
                    <div class="change-values">
                        <div class="value-change">
                            <span class="previous-value">${change.previous_value}</span>
                            <i class="fas fa-arrow-right"></i>
                            <span class="current-value">${change.current_value}</span>
                        </div>
                        <div class="change-type ${changeTypeClass}">${change.change_type}</div>
                    </div>
                    <div class="change-meta">
                        <span class="priority-badge ${priorityClass}">${change.priority}</span>
                        <span class="bulk-badge">${change.bulk_category}</span>
                    </div>
                </div>
                <div class="change-actions">
                    <input type="checkbox" class="change-checkbox" ${isSelected ? 'checked' : ''}>
                </div>
            </div>
        `;
    }

    /**
     * Attach selection event listeners
     */
    attachSelectionListeners() {
        const changeItems = document.querySelectorAll('.change-item');
        
        changeItems.forEach(item => {
            const checkbox = item.querySelector('.change-checkbox');
            const changeId = parseInt(item.dataset.changeId);
            
            const toggleSelection = () => {
                if (this.selectedChanges.has(changeId)) {
                    this.selectedChanges.delete(changeId);
                    item.classList.remove('selected');
                    checkbox.checked = false;
                } else {
                    this.selectedChanges.add(changeId);
                    item.classList.add('selected');
                    checkbox.checked = true;
                }
                this.updateSelectionCount();
            };
            
            // Click on item or checkbox
            item.addEventListener('click', (e) => {
                if (e.target.type !== 'checkbox') {
                    toggleSelection();
                }
            });
            
            checkbox.addEventListener('change', toggleSelection);
        });
    }

    /**
     * Update selection count display
     */
    updateSelectionCount() {
        const countElement = document.querySelector('.summary-stats .stat-item:nth-child(3) .stat-number');
        if (countElement) {
            countElement.textContent = this.selectedChanges.size;
        }
    }

    /**
     * Generate pre-report
     */
    async generatePreReport() {
        if (this.selectedChanges.size === 0) {
            alert('Please select at least one change for the pre-report.');
            return;
        }

        try {
            console.log('📊 Generating Pre-Report...');
            
            // Get selected changes
            const selectedData = this.filteredData.filter(change => 
                this.selectedChanges.has(change.id)
            );
            
            // Get shared configuration
            const config = window.dualReportingManager ? 
                window.dualReportingManager.sharedConfig : 
                { generatedBy: 'System', designation: 'Auditor' };
            
            console.log(`📋 Generating pre-report with ${selectedData.length} selected changes`);
            console.log('👤 Generated by:', config.generatedBy);
            console.log('🏢 Designation:', config.designation);
            
            // Here you would call the actual report generation API
            // For now, show success message
            alert(`Pre-Report generated successfully!\n\nSelected Changes: ${selectedData.length}\nGenerated By: ${config.generatedBy}\nDesignation: ${config.designation}`);
            
        } catch (error) {
            console.error('❌ Failed to generate pre-report:', error);
            alert('Failed to generate pre-report: ' + error.message);
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        console.error('❌ PreReportingEnhanced Error:', message);
        const container = document.getElementById('pre-reporting-data-container');
        if (container) {
            container.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>Error</h3>
                    <p>${message}</p>
                </div>
            `;
        }
    }
}

// Export for global access
window.PreReportingEnhanced = PreReportingEnhanced;

console.log('✅ PreReportingEnhanced module loaded');
