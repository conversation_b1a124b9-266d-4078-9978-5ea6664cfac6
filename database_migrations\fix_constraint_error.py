#!/usr/bin/env python3
"""
Fix Database Constraint Error
=============================

This script fixes the CHECK constraint error in the promotion_transfer_detection table
where 'TRANSFER_MINISTER' is corrupted and causing constraint violations.

The error: CHECK constraint failed: detection_type IN ('PROMOTION_STAFF', 'PROMOTION_MINISTER', 'TRANSFER_STAFFF', 'TRANSFER_MINISTER')

Root cause: The CHECK constraint in the database has corrupted values.
"""

import sqlite3
import os
import shutil
from datetime import datetime

def backup_database(db_path):
    """Create a backup of the database before making changes"""
    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        return None
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{db_path}.backup_{timestamp}"
    
    try:
        shutil.copy2(db_path, backup_path)
        print(f"💾 Database backed up to: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"⚠️ Failed to create backup: {e}")
        return None

def fix_constraint_error(db_path='./data/templar_payroll_auditor.db'):
    """Fix the CHECK constraint error in promotion_transfer_detection table"""
    
    print("🔧 FIXING DATABASE CONSTRAINT ERROR")
    print("=" * 50)
    
    # Create backup
    backup_path = backup_database(db_path)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check current constraint
        print("\n1. 📊 CHECKING CURRENT CONSTRAINT:")
        cursor.execute('SELECT sql FROM sqlite_master WHERE name=?', ('promotion_transfer_detection',))
        current_schema = cursor.fetchone()
        if current_schema:
            print("Current schema:")
            print(current_schema[0])
        
        # 2. Get existing data
        print("\n2. 💾 BACKING UP EXISTING DATA:")
        cursor.execute('SELECT * FROM promotion_transfer_detection')
        existing_data = cursor.fetchall()
        print(f"Found {len(existing_data)} existing records")
        
        # 3. Drop and recreate table with correct constraint
        print("\n3. 🔄 RECREATING TABLE WITH CORRECT CONSTRAINT:")
        
        # Drop the table
        cursor.execute('DROP TABLE IF EXISTS promotion_transfer_detection')
        
        # Recreate with correct constraint (split into multiple lines to avoid truncation)
        cursor.execute("""
            CREATE TABLE promotion_transfer_detection (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                employee_id TEXT NOT NULL,
                employee_name TEXT,
                detection_type TEXT NOT NULL,
                previous_value TEXT,
                current_value TEXT,
                detection_confidence REAL DEFAULT 1.0,
                rule_applied TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                department TEXT,
                detection_data TEXT,
                FOREIGN KEY (session_id) REFERENCES sessions(session_id),
                CHECK (detection_type IN ('PROMOTION_STAFF', 'PROMOTION_MINISTER', 'TRANSFER_STAFF', 'TRANSFER_MINISTER'))
            )
        """)
        print("✅ Table recreated with correct constraint")
        
        # 4. Restore data
        print("\n4. 📥 RESTORING DATA:")
        if existing_data:
            # Insert data back (excluding the auto-increment id)
            for row in existing_data:
                cursor.execute("""
                    INSERT INTO promotion_transfer_detection 
                    (session_id, employee_id, employee_name, detection_type, previous_value, 
                     current_value, detection_confidence, rule_applied, created_at, department, detection_data)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, row[1:])  # Skip the id column
            print(f"✅ Restored {len(existing_data)} records")
        
        # 5. Verify the fix
        print("\n5. ✅ VERIFYING FIX:")
        cursor.execute('SELECT sql FROM sqlite_master WHERE name=?', ('promotion_transfer_detection',))
        new_schema = cursor.fetchone()
        if new_schema:
            print("New schema:")
            print(new_schema[0])
            
            # Check if constraint is correct
            if "'TRANSFER_STAFF', 'TRANSFER_MINISTER'" in new_schema[0]:
                print("✅ Constraint fixed successfully")
            else:
                print("❌ Constraint still has issues")
                return False
        
        # Test the constraint
        print("\n6. 🧪 TESTING CONSTRAINT:")
        try:
            cursor.execute("""
                INSERT INTO promotion_transfer_detection 
                (session_id, employee_id, employee_name, detection_type)
                VALUES ('test', 'TEST001', 'Test Employee', 'TRANSFER_STAFF')
            """)
            cursor.execute("DELETE FROM promotion_transfer_detection WHERE session_id = 'test'")
            print("✅ TRANSFER_STAFF constraint test passed")
        except Exception as e:
            print(f"❌ Constraint test failed: {e}")
            return False
        
        # Commit changes
        conn.commit()
        conn.close()
        
        print("\n✅ DATABASE CONSTRAINT ERROR FIXED SUCCESSFULLY!")
        if backup_path:
            print(f"💾 Backup available at: {backup_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = fix_constraint_error()
    if success:
        print("\n🎉 Constraint error fix completed successfully!")
    else:
        print("\n💥 Constraint error fix failed!")
