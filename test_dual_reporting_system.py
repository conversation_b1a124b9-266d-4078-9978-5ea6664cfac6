#!/usr/bin/env python3
"""
COMPREHENSIVE DUAL REPORTING SYSTEM TEST SUITE
Tests all implemented functionality including dual UI, report generation, business rules, and data integrity
"""

import sqlite3
import os
import json
import sys
from datetime import datetime

def test_database_schema():
    """Test that all required database tables exist with correct schema"""
    
    print("🔍 TESTING DATABASE SCHEMA")
    print("=" * 50)
    
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Required tables for dual reporting system
        required_tables = {
            'business_rules': [
                'id', 'rule_name', 'rule_type', 'rule_condition', 
                'rule_action', 'is_active', 'created_at', 'updated_at'
            ],
            'report_history': [
                'id', 'session_id', 'report_type', 'format', 
                'generated_by', 'designation', 'file_path', 'generation_time'
            ],
            'change_classification': [
                'id', 'session_id', 'change_id', 'priority_level', 
                'bulk_category', 'bulk_size', 'classification_reason'
            ],
            'promotion_transfer_detection': [
                'id', 'session_id', 'employee_id', 'employee_name', 
                'detection_type', 'previous_value', 'current_value'
            ],
            'smart_report_config': [
                'id', 'session_id', 'report_type', 'report_format', 
                'generated_by', 'designation'
            ]
        }
        
        all_passed = True
        
        for table_name, expected_columns in required_tables.items():
            print(f"\n📊 Testing table: {table_name}")
            
            # Check if table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            table_exists = cursor.fetchone() is not None
            
            if not table_exists:
                print(f"   ❌ Table {table_name} does not exist")
                all_passed = False
                continue
            
            print(f"   ✅ Table {table_name} exists")
            
            # Check table schema
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            actual_columns = [col[1] for col in columns]
            
            missing_columns = set(expected_columns) - set(actual_columns)
            if missing_columns:
                print(f"   ❌ Missing columns: {missing_columns}")
                all_passed = False
            else:
                print(f"   ✅ All required columns present ({len(actual_columns)} columns)")
            
            # Check record count
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   📈 Records: {count}")
        
        conn.close()
        
        if all_passed:
            print("\n✅ DATABASE SCHEMA TEST PASSED")
        else:
            print("\n❌ DATABASE SCHEMA TEST FAILED")
            
        return all_passed
        
    except Exception as e:
        print(f"\n❌ Database schema test failed: {str(e)}")
        return False

def test_business_rules_data():
    """Test that business rules are properly loaded"""
    
    print("\n🔍 TESTING BUSINESS RULES DATA")
    print("=" * 50)
    
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check business rules
        cursor.execute("SELECT * FROM business_rules WHERE is_active = 1")
        active_rules = cursor.fetchall()
        
        print(f"📊 Active business rules: {len(active_rules)}")
        
        # Expected rule types based on FINAL REPORT requirements
        expected_rule_types = ['PROMOTION', 'TRANSFER', 'LOAN', 'GENERAL']
        
        cursor.execute("SELECT DISTINCT rule_type FROM business_rules WHERE is_active = 1")
        actual_rule_types = [row[0] for row in cursor.fetchall()]
        
        print(f"📋 Rule types found: {actual_rule_types}")
        
        # Check for required strict rules
        strict_rules = [
            'Staff Promotion Detection',
            'Minister Promotion Detection', 
            'Staff Transfer Detection',
            'Minister Transfer Detection',
            'Loan Balance B/F Increase',
            'Loan Current Deduction Increase'
        ]
        
        all_strict_rules_present = True
        for rule_name in strict_rules:
            cursor.execute("SELECT COUNT(*) FROM business_rules WHERE rule_name = ? AND is_active = 1", (rule_name,))
            count = cursor.fetchone()[0]
            
            if count > 0:
                print(f"   ✅ {rule_name}")
            else:
                print(f"   ❌ {rule_name} - MISSING")
                all_strict_rules_present = False
        
        conn.close()
        
        if all_strict_rules_present and len(active_rules) >= 6:
            print("\n✅ BUSINESS RULES DATA TEST PASSED")
            return True
        else:
            print("\n❌ BUSINESS RULES DATA TEST FAILED")
            return False
            
    except Exception as e:
        print(f"\n❌ Business rules data test failed: {str(e)}")
        return False

def test_file_structure():
    """Test that all required files exist"""
    
    print("\n🔍 TESTING FILE STRUCTURE")
    print("=" * 50)
    
    required_files = [
        'index.html',
        'ui/dual_reporting_styles.css',
        'ui/dual_reporting_manager.js',
        'ui/pre_reporting_enhanced.js',
        'ui/smart_reporter.js',
        'ui/business_rules_engine.js',
        'database_migrations/add_dual_reporting_tables.py',
        'DUAL_REPORTING_ARCHITECTURE.md'
    ]
    
    all_files_exist = True
    
    for file_path in required_files:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"   ✅ {file_path} ({file_size} bytes)")
        else:
            print(f"   ❌ {file_path} - MISSING")
            all_files_exist = False
    
    if all_files_exist:
        print("\n✅ FILE STRUCTURE TEST PASSED")
    else:
        print("\n❌ FILE STRUCTURE TEST FAILED")
        
    return all_files_exist

def test_html_integration():
    """Test that HTML includes all necessary components"""
    
    print("\n🔍 TESTING HTML INTEGRATION")
    print("=" * 50)
    
    try:
        with open('index.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Check for dual reporting CSS
        css_included = 'dual_reporting_styles.css' in html_content
        print(f"   {'✅' if css_included else '❌'} Dual reporting CSS included")
        
        # Check for JavaScript files
        js_files = [
            'dual_reporting_manager.js',
            'pre_reporting_enhanced.js', 
            'smart_reporter.js',
            'business_rules_engine.js'
        ]
        
        js_all_included = True
        for js_file in js_files:
            included = js_file in html_content
            print(f"   {'✅' if included else '❌'} {js_file} included")
            if not included:
                js_all_included = False
        
        # Check for dual reporting HTML structure
        dual_reporting_elements = [
            'dual-report-configuration',
            'reporting-system-toggle',
            'pre-reporting-side',
            'smart-reporter-side',
            'business-rules-list'
        ]
        
        elements_all_present = True
        for element_id in dual_reporting_elements:
            present = f'id="{element_id}"' in html_content
            print(f"   {'✅' if present else '❌'} Element #{element_id} present")
            if not present:
                elements_all_present = False
        
        all_passed = css_included and js_all_included and elements_all_present
        
        if all_passed:
            print("\n✅ HTML INTEGRATION TEST PASSED")
        else:
            print("\n❌ HTML INTEGRATION TEST FAILED")
            
        return all_passed
        
    except Exception as e:
        print(f"\n❌ HTML integration test failed: {str(e)}")
        return False

def test_css_completeness():
    """Test that CSS file contains all necessary styles"""
    
    print("\n🔍 TESTING CSS COMPLETENESS")
    print("=" * 50)
    
    try:
        with open('ui/dual_reporting_styles.css', 'r', encoding='utf-8') as f:
            css_content = f.read()
        
        # Check for key CSS classes
        required_classes = [
            '.dual-reporting-header',
            '.dual-config-section',
            '.toggle-section',
            '.reporting-side',
            '.business-rules-pane',
            '.rule-category',
            '.rule-item',
            '.employee-group',
            '.change-item'
        ]
        
        all_classes_present = True
        for css_class in required_classes:
            present = css_class in css_content
            print(f"   {'✅' if present else '❌'} {css_class}")
            if not present:
                all_classes_present = False
        
        # Check file size (should be substantial)
        file_size = len(css_content)
        size_adequate = file_size > 10000  # At least 10KB
        print(f"   {'✅' if size_adequate else '❌'} CSS file size adequate ({file_size} bytes)")
        
        all_passed = all_classes_present and size_adequate
        
        if all_passed:
            print("\n✅ CSS COMPLETENESS TEST PASSED")
        else:
            print("\n❌ CSS COMPLETENESS TEST FAILED")
            
        return all_passed
        
    except Exception as e:
        print(f"\n❌ CSS completeness test failed: {str(e)}")
        return False

def generate_test_report():
    """Generate a comprehensive test report"""
    
    print("\n" + "=" * 70)
    print("🎯 DUAL REPORTING SYSTEM - COMPREHENSIVE TEST REPORT")
    print("=" * 70)
    
    test_results = {}
    
    # Run all tests
    test_results['database_schema'] = test_database_schema()
    test_results['business_rules_data'] = test_business_rules_data()
    test_results['file_structure'] = test_file_structure()
    test_results['html_integration'] = test_html_integration()
    test_results['css_completeness'] = test_css_completeness()
    
    # Calculate overall results
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results.values() if result)
    failed_tests = total_tests - passed_tests
    
    print(f"\n📊 TEST SUMMARY:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Passed: {passed_tests}")
    print(f"   Failed: {failed_tests}")
    print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    # Detailed results
    print(f"\n📋 DETAILED RESULTS:")
    for test_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name.replace('_', ' ').title()}: {status}")
    
    # Overall status
    if all(test_results.values()):
        print(f"\n🎉 ALL TESTS PASSED - DUAL REPORTING SYSTEM READY!")
        print("   The dual reporting system has been successfully implemented.")
        print("   All components are properly integrated and functional.")
        return True
    else:
        print(f"\n⚠️ SOME TESTS FAILED - REVIEW REQUIRED")
        print("   Please address the failed tests before proceeding.")
        return False

def main():
    """Main test execution"""
    
    print("🚀 STARTING DUAL REPORTING SYSTEM COMPREHENSIVE TESTS")
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Change to the correct directory
    if not os.path.exists('index.html'):
        print("❌ Please run this test from the project root directory")
        sys.exit(1)
    
    # Run comprehensive tests
    success = generate_test_report()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
