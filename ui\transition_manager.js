/**
 * TRANSITION MANAGER
 * Handles smooth transitions, animations, and enhanced user experience
 */

class TransitionManager {
    constructor() {
        this.activeTransitions = new Map();
        this.transitionQueue = [];
        this.isTransitioning = false;
        
        console.log('🎬 TransitionManager initialized');
        this.initialize();
    }

    /**
     * Initialize transition systems
     */
    initialize() {
        this.setupPageTransitions();
        this.setupSmoothScrolling();
        this.setupStaggeredAnimations();
        this.setupAccordionTransitions();
        this.setupTabTransitions();
        
        console.log('✅ Transition systems initialized');
    }

    /**
     * Setup page transition effects
     */
    setupPageTransitions() {
        // Add page transition class to main containers
        const containers = document.querySelectorAll('.side-panel, .main-content');
        containers.forEach(container => {
            container.classList.add('page-transition');
        });

        console.log('📄 Page transitions setup complete');
    }

    /**
     * Setup smooth scrolling
     */
    setupSmoothScrolling() {
        // Enhanced smooth scrolling for anchor links - but exclude modals
        document.addEventListener('click', (e) => {
            // Skip if clicking on modal or modal content
            if (e.target.closest('.modal')) {
                return;
            }

            // Ensure we have a valid DOM element before calling closest()
            if (e.target && e.target.nodeType === Node.ELEMENT_NODE && typeof e.target.closest === 'function') {
                const link = e.target.closest('a[href^="#"]');
                if (link) {
                    e.preventDefault();
                    const targetId = link.getAttribute('href').substring(1);
                    const target = document.getElementById(targetId);

                    if (target) {
                        this.smoothScrollTo(target);
                    }
                }
            }
        });

        console.log('📜 Smooth scrolling setup complete');
    }

    /**
     * Smooth scroll to element
     */
    smoothScrollTo(element, options = {}) {
        const {
            duration = 800,
            offset = 0,
            easing = 'cubic-bezier(0.25, 0.8, 0.25, 1)'
        } = options;

        const targetPosition = element.offsetTop - offset;
        const startPosition = window.pageYOffset;
        const distance = targetPosition - startPosition;
        let startTime = null;

        function animation(currentTime) {
            if (startTime === null) startTime = currentTime;
            const timeElapsed = currentTime - startTime;
            const progress = Math.min(timeElapsed / duration, 1);
            
            // Cubic bezier easing
            const easedProgress = this.cubicBezier(progress, 0.25, 0.8, 0.25, 1);
            
            window.scrollTo(0, startPosition + distance * easedProgress);
            
            if (timeElapsed < duration) {
                requestAnimationFrame(animation.bind(this));
            }
        }

        requestAnimationFrame(animation.bind(this));
    }

    /**
     * Cubic bezier easing function
     */
    cubicBezier(t, x1, y1, x2, y2) {
        // Simplified cubic bezier implementation
        return t * t * (3 - 2 * t);
    }

    /**
     * Setup staggered animations
     */
    setupStaggeredAnimations() {
        // Observe elements for staggered animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateStaggered(entry.target);
                }
            });
        }, { threshold: 0.1 });

        // Observe stagger containers
        const staggerContainers = document.querySelectorAll('.stagger-container');
        staggerContainers.forEach(container => {
            observer.observe(container);
        });

        console.log('🎭 Staggered animations setup complete');
    }

    /**
     * Animate staggered elements
     */
    animateStaggered(container) {
        if (container.classList.contains('animate')) return;
        
        container.classList.add('animate');
        
        // Add stagger-item class to children if not present
        const children = Array.from(container.children);
        children.forEach((child, index) => {
            if (!child.classList.contains('stagger-item')) {
                child.classList.add('stagger-item');
            }
        });

        console.log(`🎬 Staggered animation triggered for container with ${children.length} items`);
    }

    /**
     * Setup accordion transitions
     */
    setupAccordionTransitions() {
        document.addEventListener('click', (e) => {
            // Skip if clicking on modal or modal content
            if (e.target.closest('.modal')) {
                return;
            }

            const accordionHeader = e.target.closest('.accordion-header');
            if (accordionHeader) {
                const accordionItem = accordionHeader.closest('.accordion-item');
                this.toggleAccordion(accordionItem);
            }
        });

        console.log('🪗 Accordion transitions setup complete');
    }

    /**
     * Toggle accordion with smooth transition
     */
    toggleAccordion(item) {
        const isExpanded = item.classList.contains('expanded');
        const content = item.querySelector('.accordion-content');
        
        if (isExpanded) {
            // Collapse
            content.style.maxHeight = content.scrollHeight + 'px';
            requestAnimationFrame(() => {
                content.style.maxHeight = '0px';
                item.classList.remove('expanded');
            });
        } else {
            // Expand
            item.classList.add('expanded');
            content.style.maxHeight = content.scrollHeight + 'px';
            
            // Reset max-height after transition
            setTimeout(() => {
                if (item.classList.contains('expanded')) {
                    content.style.maxHeight = 'none';
                }
            }, 400);
        }

        console.log(`🪗 Accordion ${isExpanded ? 'collapsed' : 'expanded'}`);
    }

    /**
     * Setup tab transitions
     */
    setupTabTransitions() {
        document.addEventListener('click', (e) => {
            // Skip if clicking on modal or modal content
            if (e.target.closest('.modal')) {
                return;
            }

            // Ensure we have a valid DOM element before calling closest()
            if (e.target && e.target.nodeType === Node.ELEMENT_NODE && typeof e.target.closest === 'function') {
                const tabButton = e.target.closest('.tab-button');
                if (tabButton) {
                    const tabContainer = tabButton.closest('.tab-container');
                    const targetTab = tabButton.getAttribute('data-tab');
                    this.switchTab(tabContainer, targetTab);
                }
            }
        });

        console.log('📑 Tab transitions setup complete');
    }

    /**
     * Switch tab with smooth transition
     */
    switchTab(container, targetTab) {
        const tabButtons = container.querySelectorAll('.tab-button');
        const tabContents = container.querySelectorAll('.tab-content');
        
        // Update button states
        tabButtons.forEach(button => {
            button.classList.remove('active');
            if (button.getAttribute('data-tab') === targetTab) {
                button.classList.add('active');
            }
        });
        
        // Transition content
        tabContents.forEach(content => {
            if (content.getAttribute('data-tab') === targetTab) {
                content.classList.add('active');
            } else {
                content.classList.remove('active');
            }
        });

        console.log(`📑 Switched to tab: ${targetTab}`);
    }

    /**
     * Transition between side panels
     */
    transitionSidePanel(fromPanel, toPanel) {
        if (this.isTransitioning) return;
        
        this.isTransitioning = true;
        
        // Exit animation for current panel
        if (fromPanel) {
            fromPanel.classList.add('exiting');
            fromPanel.classList.remove('entered');
        }
        
        // Enter animation for new panel
        if (toPanel) {
            toPanel.classList.add('entering');
            toPanel.style.display = 'block';
        }
        
        // Complete transition after animation
        setTimeout(() => {
            if (fromPanel) {
                fromPanel.classList.remove('exiting');
                fromPanel.classList.add('exited');
                fromPanel.style.display = 'none';
            }
            
            if (toPanel) {
                toPanel.classList.remove('entering');
                toPanel.classList.add('entered');
            }
            
            this.isTransitioning = false;
        }, 400);

        console.log('🔄 Side panel transition completed');
    }

    /**
     * Fade in element
     */
    fadeIn(element, duration = 300) {
        element.style.opacity = '0';
        element.style.display = 'block';
        element.classList.add('content-fade-in');
        
        requestAnimationFrame(() => {
            element.style.transition = `opacity ${duration}ms ease`;
            element.style.opacity = '1';
        });
        
        setTimeout(() => {
            element.classList.remove('content-fade-in');
            element.style.transition = '';
        }, duration);
    }

    /**
     * Fade out element
     */
    fadeOut(element, duration = 300) {
        element.style.transition = `opacity ${duration}ms ease`;
        element.style.opacity = '0';
        
        setTimeout(() => {
            element.style.display = 'none';
            element.style.transition = '';
        }, duration);
    }

    /**
     * Slide in element
     */
    slideIn(element, direction = 'left', duration = 300) {
        const transforms = {
            left: 'translateX(-20px)',
            right: 'translateX(20px)',
            up: 'translateY(-20px)',
            down: 'translateY(20px)'
        };
        
        element.style.opacity = '0';
        element.style.transform = transforms[direction];
        element.style.display = 'block';
        element.classList.add('content-slide-in');
        
        requestAnimationFrame(() => {
            element.style.transition = `all ${duration}ms cubic-bezier(0.25, 0.8, 0.25, 1)`;
            element.style.opacity = '1';
            element.style.transform = 'translate(0)';
        });
        
        setTimeout(() => {
            element.classList.remove('content-slide-in');
            element.style.transition = '';
            element.style.transform = '';
        }, duration);
    }

    /**
     * Bounce element
     */
    bounce(element) {
        element.classList.add('micro-bounce');
        setTimeout(() => {
            element.classList.remove('micro-bounce');
        }, 600);
    }

    /**
     * Shake element
     */
    shake(element) {
        element.classList.add('micro-shake');
        setTimeout(() => {
            element.classList.remove('micro-shake');
        }, 500);
    }

    /**
     * Highlight element with state
     */
    highlightState(element, state, duration = 2000) {
        const stateClass = `state-${state}`;
        element.classList.add('state-transition', stateClass);
        
        setTimeout(() => {
            element.classList.remove(stateClass);
            setTimeout(() => {
                element.classList.remove('state-transition');
            }, 300);
        }, duration);
    }

    /**
     * Create smooth modal transition - DISABLED
     */
    showModal(modal) {
        // NO TRANSITIONS - IMMEDIATE DISPLAY
        modal.style.display = 'flex';
    }

    /**
     * Hide modal with transition - DISABLED
     */
    hideModal(modal) {
        // NO TRANSITIONS - IMMEDIATE HIDE
        modal.style.display = 'none';
    }

    /**
     * Queue transition
     */
    queueTransition(transitionFn, delay = 0) {
        setTimeout(() => {
            transitionFn();
        }, delay);
    }

    /**
     * Batch transitions
     */
    batchTransitions(transitions) {
        transitions.forEach((transition, index) => {
            this.queueTransition(transition.fn, transition.delay || index * 100);
        });
    }
}

// Create global instance
window.transitionManager = new TransitionManager();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TransitionManager;
}

console.log('✅ TransitionManager module loaded');
