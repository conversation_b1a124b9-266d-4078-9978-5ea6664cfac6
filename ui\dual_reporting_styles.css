/* DUAL REPORTING SYSTEM STYLES */

/* Main Dual Reporting Container */
.dual-reporting-header {
  background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
  color: white;
  padding: 25px;
  border-radius: 12px 12px 0 0;
  text-align: center;
  margin-bottom: 0;
}

.dual-reporting-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.dual-reporting-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

/* Dual Report Configuration Section */
.dual-config-section {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.dual-config-section .config-header {
  margin-bottom: 15px;
}

.dual-config-section .config-header h3 {
  margin: 0 0 5px 0;
  color: #495057;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dual-config-section .config-header p {
  margin: 0;
  color: #6c757d;
  font-size: 13px;
}

.config-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.config-field {
  display: flex;
  flex-direction: column;
}

.config-field label {
  font-weight: 600;
  margin-bottom: 5px;
  color: #495057;
  font-size: 14px;
}

/* Toggle Section */
.toggle-section {
  margin: 25px 0;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.toggle-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.toggle-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 180px;
  text-align: center;
}

.toggle-option.active {
  background: #e3f2fd;
  border: 2px solid #2196f3;
}

.toggle-option i {
  font-size: 24px;
  margin-bottom: 8px;
  color: #495057;
}

.toggle-option.active i {
  color: #2196f3;
}

.toggle-option span {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
  color: #495057;
}

.toggle-option.active span {
  color: #2196f3;
}

.toggle-option small {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.3;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
}

.toggle-input {
  display: none;
}

.toggle-label {
  display: block;
  width: 60px;
  height: 30px;
  background: #ccc;
  border-radius: 15px;
  cursor: pointer;
  position: relative;
  transition: background 0.3s ease;
}

.toggle-input:checked + .toggle-label {
  background: #4caf50;
}

.toggle-slider {
  position: absolute;
  top: 3px;
  left: 3px;
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.toggle-input:checked + .toggle-label .toggle-slider {
  transform: translateX(30px);
}

/* Dual Reporting Main Container */
.dual-reporting-main {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  overflow: hidden;
  margin: 20px 0;
}

/* Reporting Sides */
.reporting-side {
  display: none;
  padding: 25px;
}

.reporting-side.active {
  display: block;
}

.side-header {
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.side-header h3 {
  margin: 0 0 8px 0;
  color: #495057;
  font-size: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.side-header p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

/* Controls Grid */
.controls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.control-group {
  display: flex;
  flex-direction: column;
}

.control-group label {
  font-weight: 600;
  margin-bottom: 5px;
  color: #495057;
  font-size: 14px;
}

/* Pre-Reporting Specific Styles */
.pre-reporting-controls {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.pre-reporting-actions {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #e9ecef;
}

/* Smart Reporter Specific Styles */
.smart-reporter-controls {
  background: #fff3e0;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.smart-reporter-actions {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #e9ecef;
}

/* Business Rules Pane */
.business-rules-pane {
  background: #f1f8e9;
  border: 1px solid #c8e6c9;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.rules-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.rules-header h4 {
  margin: 0;
  color: #2e7d32;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.rules-actions {
  display: flex;
  gap: 10px;
}

.rules-list {
  min-height: 100px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 15px;
}

/* Business Rule Items */
.business-rule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.business-rule-item.active {
  border-color: #4caf50;
  background: #f1f8e9;
}

.business-rule-item.inactive {
  border-color: #f44336;
  background: #ffebee;
  opacity: 0.7;
}

.rule-info {
  flex: 1;
}

.rule-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.rule-description {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.rule-type {
  font-size: 11px;
  color: #999;
  text-transform: uppercase;
  font-weight: 500;
}

.rule-actions {
  display: flex;
  gap: 8px;
}

.btn-toggle-rule {
  padding: 4px 8px;
  font-size: 11px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-enable {
  background: #4caf50;
  color: white;
}

.btn-disable {
  background: #f44336;
  color: white;
}

.btn-toggle-rule:hover {
  opacity: 0.8;
}

.no-rules {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

/* Smart Reporter Specific Styles */
.rules-status {
  margin-bottom: 20px;
}

.rules-status h5 {
  margin: 0 0 10px 0;
  color: #2e7d32;
  font-size: 14px;
  font-weight: 600;
}

.rules-summary {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.rule-status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #333;
}

.rule-status-item i {
  color: #4caf50;
  font-size: 10px;
}

.detection-results {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e0e0e0;
}

.detection-results h5 {
  margin: 0 0 10px 0;
  color: #1976d2;
  font-size: 14px;
  font-weight: 600;
}

.detection-stats {
  display: flex;
  gap: 15px;
}

.detection-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 6px;
  min-width: 60px;
}

.detection-count {
  font-size: 18px;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 2px;
}

.detection-label {
  font-size: 10px;
  color: #666;
  text-transform: uppercase;
  font-weight: 500;
}

/* Business Rules Engine Styles */
.rule-category {
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.category-title {
  background: #f5f5f5;
  padding: 12px 15px;
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px solid #e0e0e0;
}

.rule-count {
  background: #2196f3;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
  margin-left: auto;
}

.category-rules {
  padding: 10px;
}

.rule-item {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 8px;
  padding: 12px;
  transition: all 0.3s ease;
}

.rule-item.active {
  border-color: #4caf50;
  background: #f8fff8;
}

.rule-item.inactive {
  border-color: #f44336;
  background: #fff8f8;
  opacity: 0.7;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.rule-title {
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.strict-badge {
  background: #ff9800;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 9px;
  font-weight: 600;
  text-transform: uppercase;
}

.rule-actions {
  display: flex;
  gap: 6px;
}

.btn-toggle-rule,
.btn-edit-rule {
  padding: 4px 8px;
  font-size: 11px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-enable {
  background: #4caf50;
  color: white;
}

.btn-disable {
  background: #f44336;
  color: white;
}

.btn-edit-rule {
  background: #2196f3;
  color: white;
}

.btn-toggle-rule:disabled {
  background: #ccc;
  cursor: not-allowed;
  opacity: 0.6;
}

.btn-toggle-rule:hover:not(:disabled),
.btn-edit-rule:hover {
  opacity: 0.8;
}

.rule-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  font-style: italic;
}

/* Loading Animations and Progress Indicators */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.loading-overlay.active {
  opacity: 1;
  visibility: visible;
}

.loading-content {
  background: white;
  padding: 40px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  width: 90%;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #2196f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  color: #333;
  margin-bottom: 10px;
  font-weight: 500;
}

.loading-subtext {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 15px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #2196f3, #21cbf3);
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  font-size: 12px;
  color: #666;
  text-align: center;
}

/* Button Loading States */
.btn-loading {
  position: relative;
  pointer-events: none;
  opacity: 0.7;
}

.btn-loading::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.btn-loading .btn-text {
  opacity: 0;
}

/* Pulse Animation for Active Elements */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Slide In Animation */
.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* Success Animation */
.success-checkmark {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #4caf50;
  position: relative;
  margin: 0 auto 20px;
}

.success-checkmark::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 12px;
  height: 20px;
  border: solid white;
  border-width: 0 3px 3px 0;
  transform: translate(-50%, -60%) rotate(45deg);
}

.success-checkmark.animate {
  animation: successPop 0.6s ease-out;
}

@keyframes successPop {
  0% { transform: scale(0); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* Error Animation */
.error-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #f44336;
  position: relative;
  margin: 0 auto 20px;
}

.error-icon::before,
.error-icon::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 3px;
  height: 24px;
  background: white;
  transform: translate(-50%, -50%);
}

.error-icon::before {
  transform: translate(-50%, -50%) rotate(45deg);
}

.error-icon::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.error-icon.animate {
  animation: errorShake 0.6s ease-out;
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Skeleton Loading for Data */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.skeleton-text {
  height: 16px;
  border-radius: 4px;
  margin-bottom: 8px;
}

.skeleton-text.short {
  width: 60%;
}

.skeleton-text.medium {
  width: 80%;
}

.skeleton-text.long {
  width: 100%;
}

/* Notification Toast */
.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 16px 20px;
  max-width: 400px;
  z-index: 10001;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.toast-notification.show {
  transform: translateX(0);
}

.toast-notification.success {
  border-left: 4px solid #4caf50;
}

.toast-notification.error {
  border-left: 4px solid #f44336;
}

.toast-notification.info {
  border-left: 4px solid #2196f3;
}

.toast-title {
  font-weight: 600;
  margin-bottom: 4px;
  color: #333;
}

.toast-message {
  font-size: 14px;
  color: #666;
}

/* Enhanced Visual Feedback and Interactions */

/* General Hover Effects */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(33, 150, 243, 0.3);
  transition: box-shadow 0.3s ease;
}

/* Button Hover Effects */
.btn, button {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn:hover, button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active, button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* Ripple Effect for Buttons */
.btn::before, button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn:active::before, button:active::before {
  width: 300px;
  height: 300px;
}

/* Ripple Animation */
@keyframes ripple {
  to {
    transform: translate(-50%, -50%) scale(4);
    opacity: 0;
  }
}

/* Enhanced Toggle Buttons */
.toggle-option {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.toggle-option:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
}

.toggle-option.active {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.3);
}

.toggle-option::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.toggle-option:hover::after {
  transform: translateX(100%);
}

/* Interactive Cards */
.employee-group, .change-item, .rule-item {
  transition: all 0.3s ease;
  position: relative;
}

.employee-group:hover, .change-item:hover, .rule-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.employee-group:hover {
  border-left-width: 6px;
}

/* Status Indicators */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
  animation: statusPulse 2s infinite;
}

.status-indicator.active {
  background: #4caf50;
}

.status-indicator.inactive {
  background: #f44336;
  animation: none;
}

.status-indicator.pending {
  background: #ff9800;
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Interactive Form Elements */
select, input[type="text"], input[type="search"] {
  transition: all 0.3s ease;
  border: 2px solid #e0e0e0;
}

select:focus, input[type="text"]:focus, input[type="search"]:focus {
  border-color: #2196f3;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
  transform: scale(1.02);
}

select:hover, input[type="text"]:hover, input[type="search"]:hover {
  border-color: #bbb;
}

/* Checkbox and Radio Enhancements */
input[type="checkbox"], input[type="radio"] {
  appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid #ddd;
  border-radius: 3px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

input[type="radio"] {
  border-radius: 50%;
}

input[type="checkbox"]:hover, input[type="radio"]:hover {
  border-color: #2196f3;
  transform: scale(1.1);
}

input[type="checkbox"]:checked, input[type="radio"]:checked {
  background: #2196f3;
  border-color: #2196f3;
}

input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

input[type="radio"]:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

/* Priority Badges with Animation */
.priority-badge {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.priority-badge:hover {
  transform: scale(1.1);
}

.priority-badge.high {
  background: linear-gradient(135deg, #f44336, #d32f2f);
  animation: priorityGlow 2s infinite alternate;
}

.priority-badge.moderate {
  background: linear-gradient(135deg, #ff9800, #f57c00);
}

.priority-badge.low {
  background: linear-gradient(135deg, #4caf50, #388e3c);
}

@keyframes priorityGlow {
  0% { box-shadow: 0 0 5px rgba(244, 67, 54, 0.5); }
  100% { box-shadow: 0 0 20px rgba(244, 67, 54, 0.8); }
}

/* Change Type Indicators */
.change-type {
  position: relative;
  transition: all 0.3s ease;
}

.change-type::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 50%;
  width: 4px;
  height: 100%;
  transform: translateY(-50%);
  transition: all 0.3s ease;
}

.change-type.increased::before {
  background: #4caf50;
}

.change-type.decreased::before {
  background: #f44336;
}

.change-type.new::before {
  background: #2196f3;
}

.change-type.removed::before {
  background: #ff9800;
}

.change-type:hover::before {
  width: 6px;
  left: -12px;
}

/* Interactive Statistics */
.stat-item {
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-item:hover {
  transform: scale(1.05);
  background: rgba(33, 150, 243, 0.1);
}

.stat-item:hover .stat-number {
  color: #2196f3;
}

/* Dropdown Animations */
.dropdown-content {
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  pointer-events: none;
}

.dropdown:hover .dropdown-content,
.dropdown.active .dropdown-content {
  opacity: 1;
  transform: translateY(0);
  pointer-events: all;
}

/* Tab Animations */
.tab-button {
  position: relative;
  transition: all 0.3s ease;
}

.tab-button::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 3px;
  background: #2196f3;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.tab-button:hover::after {
  width: 80%;
}

.tab-button.active::after {
  width: 100%;
}

/* Scroll Indicators */
.scroll-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: rgba(33, 150, 243, 0.2);
  z-index: 9999;
}

.scroll-progress {
  height: 100%;
  background: linear-gradient(90deg, #2196f3, #21cbf3);
  width: 0%;
  transition: width 0.1s ease;
}

/* Tooltip Enhancements */
[data-tooltip] {
  position: relative;
  cursor: help;
}

[data-tooltip]:hover::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  animation: tooltipFadeIn 0.3s ease;
}

[data-tooltip]:hover::after {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.8);
  margin-bottom: -5px;
}

@keyframes tooltipFadeIn {
  from { opacity: 0; transform: translateX(-50%) translateY(5px); }
  to { opacity: 1; transform: translateX(-50%) translateY(0); }
}

/* Focus Indicators */
*:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.3);
}

/* Selection Feedback */
.selectable {
  transition: all 0.2s ease;
  cursor: pointer;
}

.selectable:hover {
  background: rgba(33, 150, 243, 0.1);
}

.selectable.selected {
  background: rgba(33, 150, 243, 0.2);
  border-color: #2196f3;
}

.selectable.selected::before {
  content: '✓';
  position: absolute;
  top: 8px;
  right: 8px;
  color: #2196f3;
  font-weight: bold;
}

/* Smooth Transitions and Enhanced UX */

/* Page Transition Effects */
.page-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
}

.page-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateY(-20px);
}

/* Smooth Side Panel Transitions */
.side-panel {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  transform-origin: center;
}

.side-panel.entering {
  opacity: 0;
  transform: translateX(-30px) scale(0.95);
}

.side-panel.entered {
  opacity: 1;
  transform: translateX(0) scale(1);
}

.side-panel.exiting {
  opacity: 1;
  transform: translateX(0) scale(1);
}

.side-panel.exited {
  opacity: 0;
  transform: translateX(30px) scale(0.95);
}

/* Enhanced Toggle Transitions */
.reporting-system-toggle {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toggle-slider {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(33, 150, 243, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.toggle-input:checked + .toggle-label .toggle-slider::before {
  width: 40px;
  height: 40px;
}

/* Staggered Animation for Lists */
.stagger-container .stagger-item {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.stagger-container.animate .stagger-item {
  opacity: 1;
  transform: translateY(0);
}

.stagger-container.animate .stagger-item:nth-child(1) { transition-delay: 0.1s; }
.stagger-container.animate .stagger-item:nth-child(2) { transition-delay: 0.2s; }
.stagger-container.animate .stagger-item:nth-child(3) { transition-delay: 0.3s; }
.stagger-container.animate .stagger-item:nth-child(4) { transition-delay: 0.4s; }
.stagger-container.animate .stagger-item:nth-child(5) { transition-delay: 0.5s; }
.stagger-container.animate .stagger-item:nth-child(n+6) { transition-delay: 0.6s; }

/* Smooth Content Transitions */
.content-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.content-fade-in {
  animation: contentFadeIn 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
}

@keyframes contentFadeIn {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-slide-in {
  animation: contentSlideIn 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

@keyframes contentSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Enhanced Modal Transitions - DISABLED */
.modal-overlay {
  /* NO TRANSITIONS OR BLUR EFFECTS */
}

.modal-overlay.active {
  /* NO BLUR EFFECTS */
}

.modal-content {
  /* NO TRANSITIONS OR TRANSFORMS */
  opacity: 1;
  transform: none;
}

.modal-overlay.active .modal-content {
  /* NO TRANSFORMS */
  opacity: 1;
  transform: none;
}

/* Smooth Accordion Transitions */
.accordion-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.accordion-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.accordion-item.expanded .accordion-content {
  max-height: 1000px;
}

.accordion-header {
  transition: all 0.3s ease;
}

.accordion-item.expanded .accordion-header {
  background: rgba(33, 150, 243, 0.1);
}

/* Enhanced Button Transitions */
.btn-enhanced {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-enhanced:hover::before {
  left: 100%;
}

.btn-enhanced:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-enhanced:active {
  transform: translateY(0);
  transition: transform 0.1s;
}

/* Smooth Tab Transitions */
.tab-content {
  opacity: 0;
  transform: translateX(20px);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: absolute;
  width: 100%;
}

.tab-content.active {
  opacity: 1;
  transform: translateX(0);
  position: relative;
}

.tab-container {
  position: relative;
  overflow: hidden;
}

/* Smooth Dropdown Transitions */
.dropdown-menu {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  transform-origin: top;
}

.dropdown.open .dropdown-menu {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Enhanced Card Hover Effects */
.card-enhanced {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
}

.card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.1), rgba(33, 203, 243, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
}

.card-enhanced:hover::before {
  opacity: 1;
}

.card-enhanced:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Smooth Progress Transitions */
.progress-smooth {
  transition: all 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.progress-smooth .progress-bar {
  transition: width 0.8s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* Enhanced Form Field Transitions */
.form-field-enhanced {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-field-enhanced input,
.form-field-enhanced select,
.form-field-enhanced textarea {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  background: rgba(0, 0, 0, 0.05);
}

.form-field-enhanced input:focus,
.form-field-enhanced select:focus,
.form-field-enhanced textarea:focus {
  background: white;
  border-color: #2196f3;
  transform: scale(1.02);
  box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
}

/* Smooth Notification Transitions */
.notification-enter {
  opacity: 0;
  transform: translateX(100%) scale(0.8);
}

.notification-enter-active {
  opacity: 1;
  transform: translateX(0) scale(1);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.notification-exit {
  opacity: 1;
  transform: translateX(0) scale(1);
}

.notification-exit-active {
  opacity: 0;
  transform: translateX(100%) scale(0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Loading Transitions */
.loading-enter {
  opacity: 0;
  transform: scale(0.8);
}

.loading-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.loading-exit {
  opacity: 1;
  transform: scale(1);
}

.loading-exit-active {
  opacity: 0;
  transform: scale(1.1);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth Scroll Behavior */
html {
  scroll-behavior: smooth;
}

/* Enhanced Focus Transitions */
*:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.3);
  transition: box-shadow 0.2s ease;
}

/* Micro-interactions */
.micro-bounce {
  animation: microBounce 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
}

@keyframes microBounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.micro-shake {
  animation: microShake 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

@keyframes microShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* Smooth State Transitions */
.state-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.state-success {
  background: linear-gradient(135deg, #4caf50, #45a049);
  color: white;
}

.state-error {
  background: linear-gradient(135deg, #f44336, #d32f2f);
  color: white;
}

.state-warning {
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
}

.state-info {
  background: linear-gradient(135deg, #2196f3, #1976d2);
  color: white;
}

/* Navigation */
.dual-reporting-navigation {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #e9ecef;
}

/* Button Styles */
.btn.large {
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 600;
}

.btn.small {
  padding: 6px 12px;
  font-size: 12px;
}

.btn.success {
  background: #4caf50;
  color: white;
  border: none;
}

.btn.success:hover {
  background: #45a049;
}

/* Form Input Styles */
.form-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

/* Loading Container */
.loading-container {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced Pre-Reporting Styles */
.pre-reporting-summary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.summary-stats {
  display: flex;
  justify-content: space-around;
  gap: 20px;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #2196f3;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  font-weight: 500;
}

/* Employee Groups */
.employee-group {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;
}

.employee-header {
  background: #f5f5f5;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
}

.employee-info h4 {
  margin: 0 0 4px 0;
  color: #333;
  font-size: 16px;
}

.employee-id {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.change-count {
  background: #2196f3;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

/* Change Items */
.employee-changes {
  padding: 10px;
}

.change-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.change-item:hover {
  background: #f0f0f0;
  border-color: #ccc;
}

.change-item.selected {
  background: #e3f2fd;
  border-color: #2196f3;
}

.change-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
}

.change-main {
  flex: 1;
}

.change-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.change-section {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
}

.change-values {
  flex: 1;
  text-align: center;
}

.value-change {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 4px;
}

.previous-value,
.current-value {
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 13px;
}

.previous-value {
  background: #ffebee;
  color: #d32f2f;
}

.current-value {
  background: #e8f5e8;
  color: #388e3c;
}

.value-change i {
  color: #666;
  font-size: 12px;
}

.change-type {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  padding: 2px 6px;
  border-radius: 4px;
}

.change-type.increased {
  background: #c8e6c9;
  color: #2e7d32;
}

.change-type.decreased {
  background: #ffcdd2;
  color: #c62828;
}

.change-type.new {
  background: #bbdefb;
  color: #1565c0;
}

.change-type.removed {
  background: #f8bbd9;
  color: #ad1457;
}

.change-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-end;
}

.priority-badge,
.bulk-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
  text-transform: uppercase;
}

.priority-badge.high {
  background: #ffcdd2;
  color: #c62828;
}

.priority-badge.moderate {
  background: #fff3e0;
  color: #ef6c00;
}

.priority-badge.low {
  background: #e8f5e8;
  color: #2e7d32;
}

.bulk-badge {
  background: #f3e5f5;
  color: #7b1fa2;
}

.change-actions {
  margin-left: 15px;
}

.change-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

/* Message States */
.no-data-message,
.error-message {
  text-align: center;
  padding: 40px;
  color: #666;
}

.no-data-message i,
.error-message i {
  font-size: 48px;
  margin-bottom: 15px;
  color: #ccc;
}

.error-message i {
  color: #f44336;
}

.no-data-message h3,
.error-message h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.no-data-message p,
.error-message p {
  margin: 0;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .config-grid,
  .controls-grid {
    grid-template-columns: 1fr;
  }

  .toggle-container {
    flex-direction: column;
    gap: 15px;
  }

  .toggle-option {
    min-width: auto;
    width: 100%;
  }

  .summary-stats {
    flex-direction: column;
    gap: 10px;
  }

  .change-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .change-values {
    align-self: stretch;
  }

  .value-change {
    justify-content: flex-start;
  }
}
