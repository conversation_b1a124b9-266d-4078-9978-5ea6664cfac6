/**
 * BUSINESS RULES ENGINE
 * Implements strict enforcement of promotion, transfer, and loan reporting rules
 * as specified in the FINAL REPORT requirements
 */

class BusinessRulesEngine {
    constructor() {
        this.rules = [];
        this.ruleCategories = {
            PROMOTION: 'Promotion Detection Rules',
            TRANSFER: 'Transfer Detection Rules', 
            LOAN: 'Loan Reporting Rules',
            GENERAL: 'General Reporting Rules'
        };
        this.isInitialized = false;
        
        console.log('⚙️ BusinessRulesEngine initialized');
    }

    /**
     * Initialize the business rules engine
     */
    async initialize() {
        if (this.isInitialized) {
            console.log('⚠️ BusinessRulesEngine already initialized');
            return;
        }

        try {
            console.log('🚀 Initializing Business Rules Engine...');
            
            // Load rules from database
            await this.loadRulesFromDatabase();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Render rules interface
            this.renderRulesInterface();
            
            this.isInitialized = true;
            console.log('✅ Business Rules Engine initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize Business Rules Engine:', error);
        }
    }

    /**
     * Load rules from database
     */
    async loadRulesFromDatabase() {
        try {
            // For now, use predefined rules based on FINAL REPORT requirements
            this.rules = this.getDefaultRules();
            console.log(`✅ Loaded ${this.rules.length} business rules`);
            
        } catch (error) {
            console.error('❌ Failed to load rules from database:', error);
            this.rules = this.getDefaultRules();
        }
    }

    /**
     * Get default rules based on FINAL REPORT specifications
     */
    getDefaultRules() {
        return [
            // PROMOTION RULES (Strictly enforced)
            {
                id: 'promotion-staff-rule',
                name: 'Staff Promotion Detection',
                category: 'PROMOTION',
                description: 'STAFF PROMOTION = Basic salary increase + Job Title change',
                condition: {
                    type: 'AND',
                    rules: [
                        { field: 'basic_salary', operator: 'INCREASED', value: null },
                        { field: 'job_title', operator: 'CHANGED', value: null },
                        { field: 'department', operator: 'NOT_CONTAINS', value: 'MINISTERS' }
                    ]
                },
                action: {
                    classify_as: 'PROMOTION_STAFF',
                    include_in_appendix: true,
                    appendix_section: 'PROMOTIONS STAFF'
                },
                isActive: true,
                isStrict: true // Cannot be modified
            },
            {
                id: 'promotion-minister-rule',
                name: 'Minister Promotion Detection',
                category: 'PROMOTION',
                description: 'MINISTER PROMOTION = Job title change only for persons who have Ministers in their Department value',
                condition: {
                    type: 'AND',
                    rules: [
                        { field: 'job_title', operator: 'CHANGED', value: null },
                        { field: 'department', operator: 'CONTAINS', value: 'MINISTERS' }
                    ]
                },
                action: {
                    classify_as: 'PROMOTION_MINISTER',
                    include_in_appendix: true,
                    appendix_section: 'PROMOTIONS MINISTERS'
                },
                isActive: true,
                isStrict: true
            },
            
            // TRANSFER RULES (Strictly enforced)
            {
                id: 'transfer-staff-rule',
                name: 'Staff Transfer Detection',
                category: 'TRANSFER',
                description: 'STAFF TRANSFER = Change in Department',
                condition: {
                    type: 'AND',
                    rules: [
                        { field: 'department', operator: 'CHANGED', value: null },
                        { field: 'department', operator: 'NOT_CONTAINS', value: 'MINISTERS' }
                    ]
                },
                action: {
                    classify_as: 'TRANSFER_STAFF',
                    include_in_appendix: true,
                    appendix_section: 'TRANSFERS STAFF'
                },
                isActive: true,
                isStrict: true
            },
            {
                id: 'transfer-minister-rule',
                name: 'Minister Transfer Detection',
                category: 'TRANSFER',
                description: 'MINISTER TRANSFER = Change in Department for Ministers',
                condition: {
                    type: 'AND',
                    rules: [
                        { field: 'department', operator: 'CHANGED', value: null },
                        { field: 'department', operator: 'CONTAINS', value: 'MINISTERS' }
                    ]
                },
                action: {
                    classify_as: 'TRANSFER_MINISTER',
                    include_in_appendix: true,
                    appendix_section: 'TRANSFERS MINISTER'
                },
                isActive: true,
                isStrict: true
            },
            
            // LOAN REPORTING RULES (Strictly enforced)
            {
                id: 'loan-balance-bf-rule',
                name: 'Loan Balance B/F Increase',
                category: 'LOAN',
                description: 'An increase in Balance B/F of an existing loan',
                condition: {
                    type: 'AND',
                    rules: [
                        { field: 'item_type', operator: 'EQUALS', value: 'LOAN' },
                        { field: 'balance_bf', operator: 'INCREASED', value: null }
                    ]
                },
                action: {
                    report_format: '{LOAN_TYPE} Balance increased from {PREV_VALUE} to {CURR_VALUE} in {MONTH} {YEAR}'
                },
                isActive: true,
                isStrict: true
            },
            {
                id: 'loan-current-deduction-rule',
                name: 'Loan Current Deduction Increase',
                category: 'LOAN',
                description: 'An increase of the Current Deduction',
                condition: {
                    type: 'AND',
                    rules: [
                        { field: 'item_type', operator: 'EQUALS', value: 'LOAN' },
                        { field: 'current_deduction', operator: 'INCREASED', value: null }
                    ]
                },
                action: {
                    report_format: '{LOAN_TYPE} Current Deduction increased from {PREV_VALUE} to {CURR_VALUE}'
                },
                isActive: true,
                isStrict: true
            },
            
            // GENERAL REPORTING RULES
            {
                id: 'new-employee-rule',
                name: 'New Employee Detection',
                category: 'GENERAL',
                description: 'If Employee No. is appearing in current month but not in Previous month',
                condition: {
                    type: 'OR',
                    rules: [
                        { field: 'employee_no', operator: 'NEW', value: null },
                        { field: 'employee_name', operator: 'NEW', value: null }
                    ]
                },
                action: {
                    classify_as: 'NEW_EMPLOYEE',
                    report_format: 'New employee added to payroll'
                },
                isActive: true,
                isStrict: false
            },
            {
                id: 'removed-employee-rule',
                name: 'Removed Employee Detection',
                category: 'GENERAL',
                description: 'Employee Name or Employee No. removed from the current month but was present in previous',
                condition: {
                    type: 'OR',
                    rules: [
                        { field: 'employee_no', operator: 'REMOVED', value: null },
                        { field: 'employee_name', operator: 'REMOVED', value: null }
                    ]
                },
                action: {
                    classify_as: 'REMOVED_EMPLOYEE',
                    report_format: 'Employee removed from payroll'
                },
                isActive: true,
                isStrict: false
            }
        ];
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Add rule button
        const addRuleBtn = document.getElementById('add-business-rule-btn');
        if (addRuleBtn) {
            addRuleBtn.addEventListener('click', () => {
                this.showAddRuleDialog();
            });
        }

        // Manage rules button
        const manageRulesBtn = document.getElementById('manage-rules-btn');
        if (manageRulesBtn) {
            manageRulesBtn.addEventListener('click', () => {
                this.showManageRulesDialog();
            });
        }
    }

    /**
     * Render rules interface
     */
    renderRulesInterface() {
        const rulesList = document.getElementById('business-rules-list');
        if (!rulesList) return;

        const categorizedRules = this.categorizeRules();
        
        const html = Object.entries(categorizedRules).map(([category, rules]) => {
            if (rules.length === 0) return '';
            
            return `
                <div class="rule-category">
                    <h5 class="category-title">
                        <i class="fas fa-${this.getCategoryIcon(category)}"></i>
                        ${this.ruleCategories[category]}
                        <span class="rule-count">(${rules.length})</span>
                    </h5>
                    <div class="category-rules">
                        ${rules.map(rule => this.renderRuleItem(rule)).join('')}
                    </div>
                </div>
            `;
        }).join('');

        rulesList.innerHTML = html || '<p class="no-rules">No business rules configured.</p>';
    }

    /**
     * Categorize rules by type
     */
    categorizeRules() {
        return this.rules.reduce((categories, rule) => {
            const category = rule.category || 'GENERAL';
            if (!categories[category]) {
                categories[category] = [];
            }
            categories[category].push(rule);
            return categories;
        }, {});
    }

    /**
     * Get icon for category
     */
    getCategoryIcon(category) {
        const icons = {
            PROMOTION: 'arrow-up',
            TRANSFER: 'exchange-alt',
            LOAN: 'money-bill-wave',
            GENERAL: 'cogs'
        };
        return icons[category] || 'cog';
    }

    /**
     * Render individual rule item
     */
    renderRuleItem(rule) {
        const statusClass = rule.isActive ? 'active' : 'inactive';
        const strictBadge = rule.isStrict ? '<span class="strict-badge">STRICT</span>' : '';
        
        return `
            <div class="rule-item ${statusClass}" data-rule-id="${rule.id}">
                <div class="rule-header">
                    <div class="rule-title">
                        ${rule.name}
                        ${strictBadge}
                    </div>
                    <div class="rule-actions">
                        <button class="btn-toggle-rule ${rule.isActive ? 'btn-disable' : 'btn-enable'}" 
                                onclick="window.businessRulesEngine.toggleRule('${rule.id}')"
                                ${rule.isStrict ? 'disabled title="Strict rules cannot be disabled"' : ''}>
                            ${rule.isActive ? 'Disable' : 'Enable'}
                        </button>
                        ${!rule.isStrict ? `<button class="btn-edit-rule" onclick="window.businessRulesEngine.editRule('${rule.id}')">Edit</button>` : ''}
                    </div>
                </div>
                <div class="rule-description">${rule.description}</div>
            </div>
        `;
    }

    /**
     * Toggle rule active state
     */
    toggleRule(ruleId) {
        const rule = this.rules.find(r => r.id === ruleId);
        if (!rule) return;

        if (rule.isStrict) {
            alert('Strict rules cannot be disabled. These rules are required for compliance with reporting standards.');
            return;
        }

        rule.isActive = !rule.isActive;
        this.renderRulesInterface();
        
        console.log(`🔄 Toggled rule ${ruleId}: ${rule.isActive ? 'enabled' : 'disabled'}`);
    }

    /**
     * Edit rule
     */
    editRule(ruleId) {
        const rule = this.rules.find(r => r.id === ruleId);
        if (!rule) return;

        if (rule.isStrict) {
            alert('Strict rules cannot be edited. These rules are enforced for compliance.');
            return;
        }

        console.log('✏️ Edit rule:', rule);
        // Edit dialog will be implemented in next phase
        alert('Rule editing interface will be available in the next update.');
    }

    /**
     * Show add rule dialog
     */
    showAddRuleDialog() {
        console.log('➕ Show add rule dialog');
        // Add rule dialog will be implemented in next phase
        alert('Add rule interface will be available in the next update.');
    }

    /**
     * Show manage rules dialog
     */
    showManageRulesDialog() {
        console.log('⚙️ Show manage rules dialog');
        // Manage rules dialog will be implemented in next phase
        alert('Manage rules interface will be available in the next update.');
    }

    /**
     * Apply rules to data
     */
    applyRules(data) {
        console.log('🔍 Applying business rules to data...');
        
        const results = {
            promotions: [],
            transfers: [],
            loanChanges: [],
            generalChanges: []
        };

        // Group data by employee
        const employeeGroups = this.groupByEmployee(data);

        // Apply rules to each employee
        for (const [employeeId, changes] of Object.entries(employeeGroups)) {
            const employee = changes[0];
            
            // Apply promotion rules
            this.applyPromotionRules(employee, changes, results);
            
            // Apply transfer rules
            this.applyTransferRules(employee, changes, results);
            
            // Apply loan rules
            this.applyLoanRules(employee, changes, results);
        }

        console.log('✅ Business rules applied:', results);
        return results;
    }

    /**
     * Apply promotion rules
     */
    applyPromotionRules(employee, changes, results) {
        const activePromotionRules = this.rules.filter(r => 
            r.category === 'PROMOTION' && r.isActive
        );

        for (const rule of activePromotionRules) {
            if (this.evaluateRule(rule, employee, changes)) {
                results.promotions.push({
                    ruleId: rule.id,
                    employee: employee,
                    type: rule.action.classify_as,
                    appendixSection: rule.action.appendix_section
                });
            }
        }
    }

    /**
     * Apply transfer rules
     */
    applyTransferRules(employee, changes, results) {
        const activeTransferRules = this.rules.filter(r => 
            r.category === 'TRANSFER' && r.isActive
        );

        for (const rule of activeTransferRules) {
            if (this.evaluateRule(rule, employee, changes)) {
                results.transfers.push({
                    ruleId: rule.id,
                    employee: employee,
                    type: rule.action.classify_as,
                    appendixSection: rule.action.appendix_section
                });
            }
        }
    }

    /**
     * Apply loan rules
     */
    applyLoanRules(employee, changes, results) {
        const activeLoanRules = this.rules.filter(r => 
            r.category === 'LOAN' && r.isActive
        );

        for (const rule of activeLoanRules) {
            if (this.evaluateRule(rule, employee, changes)) {
                results.loanChanges.push({
                    ruleId: rule.id,
                    employee: employee,
                    reportFormat: rule.action.report_format
                });
            }
        }
    }

    /**
     * Evaluate rule against employee data
     */
    evaluateRule(rule, employee, changes) {
        // Simplified rule evaluation - will be enhanced
        console.log(`🔍 Evaluating rule: ${rule.name}`);
        return false; // Placeholder
    }

    /**
     * Group data by employee
     */
    groupByEmployee(data) {
        return data.reduce((groups, change) => {
            const key = change.employee_id;
            if (!groups[key]) {
                groups[key] = [];
            }
            groups[key].push(change);
            return groups;
        }, {});
    }
}

// Export for global access
window.BusinessRulesEngine = BusinessRulesEngine;

console.log('✅ BusinessRulesEngine module loaded');
