#!/usr/bin/env python3
"""
EMPLOYEE-BASED REPORT GENERATOR
Generates employee-focused reports following the FINAL REPORT template format
"""

import json
import sqlite3
from datetime import datetime
from typing import Dict, List, Any, Optional
from collections import defaultdict

# Import the promotion/transfer detector
try:
    from core.promotion_transfer_detector import PromotionTransferDetector
    DETECTOR_AVAILABLE = True
except ImportError:
    DETECTOR_AVAILABLE = False

class EmployeeBasedReportGenerator:
    """
    Generates employee-based reports with proper formatting and business rule application
    """
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or './data/templar_payroll_auditor.db'
        self.report_data = {}
        self.promotions = []
        self.transfers = []
        self.new_employees = []
        self.removed_employees = []
        
    def generate_report(self, session_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate employee-based report for the given session
        
        Args:
            session_id: Session identifier
            config: Report configuration (generated_by, designation, etc.)
            
        Returns:
            Dict containing the generated report data
        """
        try:
            print(f"🎯 Generating Employee-Based Report for session: {session_id}")
            
            # Load data from database
            report_data = self._load_report_data(session_id)
            
            if not report_data:
                raise Exception("No report data found for session")
            
            # Apply business rules and detect special cases
            self._apply_business_rules(report_data)

            # Run advanced promotion/transfer detection
            self._run_advanced_detection(session_id)
            
            # Generate report structure
            report = self._build_employee_based_report(report_data, config)
            
            # Add appendix sections
            self._add_appendix_sections(report)
            
            print(f"✅ Employee-Based Report generated successfully")
            return {
                'success': True,
                'report': report,
                'metadata': {
                    'report_type': 'employee-based',
                    'session_id': session_id,
                    'generated_at': datetime.now().isoformat(),
                    'total_employees': len(report_data),
                    'total_changes': sum(len(emp['changes']) for emp in report_data.values()),
                    'promotions': len(self.promotions),
                    'transfers': len(self.transfers)
                }
            }
            
        except Exception as e:
            print(f"❌ Failed to generate employee-based report: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _load_report_data(self, session_id: str) -> Dict[str, Any]:
        """Load pre-reporting data from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # First, ensure employees table is populated with department data from comparison_results
            self._populate_employees_table(cursor, session_id)

            # Load comparison results with pre-reporting classifications
            query = '''
                SELECT cr.id, cr.employee_id, cr.employee_name, e.department, cr.section_name,
                       cr.item_label, cr.previous_value, cr.current_value, cr.change_type,
                       cr.priority, cr.numeric_difference, cr.percentage_change,
                       pr.bulk_category, pr.bulk_size, pr.selected_for_report
                FROM comparison_results cr
                LEFT JOIN employees e ON cr.employee_id = e.employee_id AND e.session_id = cr.session_id
                LEFT JOIN pre_reporting_results pr ON cr.id = pr.change_id
                WHERE cr.session_id = ? AND (pr.selected_for_report = 1 OR pr.selected_for_report IS NULL)
                ORDER BY cr.employee_id, cr.section_name, cr.item_label
            '''

            cursor.execute(query, (session_id,))
            results = cursor.fetchall()

            # Group by employee
            employee_data = defaultdict(lambda: {
                'employee_id': '',
                'employee_name': '',
                'department': '',
                'changes': []
            })

            for row in results:
                emp_id = row[1]
                if not employee_data[emp_id]['employee_id']:
                    employee_data[emp_id]['employee_id'] = row[1]
                    employee_data[emp_id]['employee_name'] = row[2]
                    employee_data[emp_id]['department'] = row[3] or ''

                change = {
                    'id': row[0],
                    'section_name': row[4],
                    'item_label': row[5],
                    'previous_value': row[6],
                    'current_value': row[7],
                    'change_type': row[8],
                    'priority': row[9],
                    'numeric_difference': row[10],
                    'percentage_change': row[11],
                    'bulk_category': row[12],
                    'bulk_size': row[13]
                }

                employee_data[emp_id]['changes'].append(change)
            
            conn.close()
            
            print(f"✅ Loaded data for {len(employee_data)} employees")
            return dict(employee_data)
            
        except Exception as e:
            print(f"❌ Failed to load report data: {str(e)}")
            return {}

    def _populate_employees_table(self, cursor, session_id: str):
        """Populate employees table with data from comparison_results if it's empty"""
        try:
            # Check if employees table already has data for this session
            cursor.execute('SELECT COUNT(*) FROM employees WHERE session_id = ?', (session_id,))
            existing_count = cursor.fetchone()[0]

            if existing_count > 0:
                print(f"   ✅ Employees table already populated ({existing_count} employees)")
                return

            print(f"   🔄 Populating employees table from comparison_results...")

            # Get unique employees from comparison_results with their department info
            # Extract department from both current_value and previous_value to capture all available data
            cursor.execute('''
                SELECT DISTINCT
                    cr.employee_id,
                    cr.employee_name,
                    COALESCE(
                        dept.current_value,
                        dept.previous_value,
                        ''
                    ) as department,
                    COALESCE(
                        section.current_value,
                        section.previous_value,
                        ''
                    ) as section,
                    COALESCE(
                        job.current_value,
                        job.previous_value,
                        ''
                    ) as job_title
                FROM comparison_results cr
                LEFT JOIN comparison_results dept ON cr.employee_id = dept.employee_id
                    AND dept.session_id = cr.session_id
                    AND dept.item_label = 'DEPARTMENT'
                LEFT JOIN comparison_results section ON cr.employee_id = section.employee_id
                    AND section.session_id = cr.session_id
                    AND section.item_label = 'SECTION'
                LEFT JOIN comparison_results job ON cr.employee_id = job.employee_id
                    AND job.session_id = cr.session_id
                    AND job.item_label = 'JOB TITLE'
                WHERE cr.session_id = ?
                ORDER BY cr.employee_id
            ''', (session_id,))

            employees = cursor.fetchall()

            # Insert employees into employees table
            insert_count = 0
            for emp in employees:
                cursor.execute('''
                    INSERT INTO employees
                    (session_id, employee_id, employee_name, department, section, job_title, period_type)
                    VALUES (?, ?, ?, ?, ?, ?, 'current')
                ''', (session_id, emp[0], emp[1], emp[2], emp[3], emp[4]))
                insert_count += 1

            # Commit the transaction
            cursor.connection.commit()
            print(f"   ✅ Populated employees table with {insert_count} employees")

        except Exception as e:
            print(f"   ⚠️ Error populating employees table: {str(e)}")

    def _apply_business_rules(self, report_data: Dict[str, Any]):
        """Apply business rules to detect promotions, transfers, etc."""
        print("🔍 Applying business rules...")
        
        self.promotions = []
        self.transfers = []
        self.new_employees = []
        self.removed_employees = []
        
        for emp_id, emp_data in report_data.items():
            changes = emp_data['changes']
            
            # Check for promotions
            promotion = self._detect_promotion(emp_data, changes)
            if promotion:
                self.promotions.append(promotion)
            
            # Check for transfers
            transfer = self._detect_transfer(emp_data, changes)
            if transfer:
                self.transfers.append(transfer)
            
            # Check for new employees
            if self._is_new_employee(changes):
                self.new_employees.append(emp_data)
            
            # Check for removed employees (would be handled differently)
            # This would come from a separate query for employees in previous but not current
        
        print(f"✅ Detected: {len(self.promotions)} promotions, {len(self.transfers)} transfers")

    def _run_advanced_detection(self, session_id: str):
        """Run advanced promotion/transfer detection"""
        if not DETECTOR_AVAILABLE:
            print("⚠️ Advanced detector not available, using basic detection")
            return

        try:
            print("🔍 Running advanced promotion/transfer detection...")

            detector = PromotionTransferDetector(self.db_path)
            detection_result = detector.detect_all_changes(session_id)

            if detection_result.get('success'):
                # Update our results with advanced detection
                advanced_results = detection_result['detection_results']

                # Merge staff promotions
                for promotion in advanced_results['promotions']['staff']:
                    self.promotions.append({
                        'type': 'STAFF',
                        'employee_id': promotion['employee_id'],
                        'employee_name': promotion['employee_name'],
                        'department': promotion['department'],
                        'rule_applied': 'Advanced Staff Promotion Detection',
                        'details': promotion
                    })

                # Merge minister promotions
                for promotion in advanced_results['promotions']['ministers']:
                    self.promotions.append({
                        'type': 'MINISTER',
                        'employee_id': promotion['employee_id'],
                        'employee_name': promotion['employee_name'],
                        'department': promotion['department'],
                        'rule_applied': 'Advanced Minister Promotion Detection',
                        'details': promotion
                    })

                # Merge staff transfers
                for transfer in advanced_results['transfers']['staff']:
                    self.transfers.append({
                        'type': 'STAFF',
                        'employee_id': transfer['employee_id'],
                        'employee_name': transfer['employee_name'],
                        'department': transfer['current_department'],
                        'rule_applied': 'Advanced Staff Transfer Detection',
                        'details': transfer
                    })

                # Merge minister transfers
                for transfer in advanced_results['transfers']['ministers']:
                    self.transfers.append({
                        'type': 'MINISTER',
                        'employee_id': transfer['employee_id'],
                        'employee_name': transfer['employee_name'],
                        'department': transfer['current_department'],
                        'rule_applied': 'Advanced Minister Transfer Detection',
                        'details': transfer
                    })

                # Update new employees
                self.new_employees.extend(advanced_results['new_employees'])

                print(f"✅ Advanced detection complete: {len(self.promotions)} total promotions, {len(self.transfers)} total transfers")

        except Exception as e:
            print(f"⚠️ Advanced detection failed: {str(e)}")
            # Continue with basic detection results
    
    def _detect_promotion(self, emp_data: Dict[str, Any], changes: List[Dict]) -> Optional[Dict]:
        """Detect promotion based on business rules"""
        
        # Check for basic salary increase
        has_salary_increase = any(
            change['item_label'] == 'BASIC_SALARY' and change['change_type'] == 'INCREASED'
            for change in changes
        )
        
        # Check for job title change
        has_job_title_change = any(
            change['item_label'] == 'JOB_TITLE' and change['change_type'] == 'CHANGED'
            for change in changes
        )
        
        # Check if it's a minister
        is_minister = 'MINISTERS' in (emp_data.get('department') or '').upper()
        
        # Apply promotion rules
        if has_salary_increase and has_job_title_change and not is_minister:
            # Staff promotion
            return {
                'type': 'STAFF',
                'employee_id': emp_data['employee_id'],
                'employee_name': emp_data['employee_name'],
                'department': emp_data['department'],
                'rule_applied': 'Staff Promotion Detection'
            }
        elif has_job_title_change and is_minister:
            # Minister promotion
            return {
                'type': 'MINISTER',
                'employee_id': emp_data['employee_id'],
                'employee_name': emp_data['employee_name'],
                'department': emp_data['department'],
                'rule_applied': 'Minister Promotion Detection'
            }
        
        return None
    
    def _detect_transfer(self, emp_data: Dict[str, Any], changes: List[Dict]) -> Optional[Dict]:
        """Detect transfer based on business rules"""
        
        # Check for department change
        has_department_change = any(
            change['item_label'] == 'DEPARTMENT' and change['change_type'] == 'CHANGED'
            for change in changes
        )
        
        if has_department_change:
            is_minister = 'MINISTERS' in (emp_data.get('department') or '').upper()
            
            return {
                'type': 'MINISTER' if is_minister else 'STAFF',
                'employee_id': emp_data['employee_id'],
                'employee_name': emp_data['employee_name'],
                'department': emp_data['department'],
                'rule_applied': 'Transfer Detection'
            }
        
        return None
    
    def _is_new_employee(self, changes: List[Dict]) -> bool:
        """Check if this represents a new employee"""
        return any(
            change['change_type'] == 'NEW' and change['section_name'] == 'PERSONAL_DETAILS'
            for change in changes
        )
    
    def _build_employee_based_report(self, report_data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Build the main employee-based report structure"""
        
        # Report header
        report = {
            'header': {
                'title': 'PAYROLL AUDIT REPORT: JULY 2025',
                'report_information': {
                    'period': 'July 2025',
                    'generated_at': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'generated_by': config.get('generated_by', 'System'),
                    'designation': config.get('designation', 'Payroll Auditor')
                },
                'executive_summary': self._generate_executive_summary(report_data)
            },
            'findings_and_observations': {},
            'new_employees': [],
            'removed_employees': []
        }
        
        # Group findings by employee
        for emp_id, emp_data in report_data.items():
            if emp_data['changes']:
                employee_section = self._format_employee_findings(emp_data)
                report['findings_and_observations'][emp_id] = employee_section
        
        # Add promotions section
        if self.promotions:
            report['promotions'] = [
                {
                    'employee_id': promo['employee_id'],
                    'name': promo['employee_name'],
                    'department': promo['department'],
                    'type': f"PROMOTION_{promo['type']}",  # Convert STAFF -> PROMOTION_STAFF
                    'rule_applied': promo.get('rule_applied', 'N/A')
                }
                for promo in self.promotions
            ]

        # Add transfers section
        if self.transfers:
            report['transfers'] = [
                {
                    'employee_id': transfer['employee_id'],
                    'name': transfer['employee_name'],
                    'department': transfer['department'],
                    'type': f"TRANSFER_{transfer['type']}",  # Convert STAFF -> TRANSFER_STAFF
                    'rule_applied': transfer.get('rule_applied', 'N/A')
                }
                for transfer in self.transfers
            ]

        # Add new employees section
        if self.new_employees:
            report['new_employees'] = [
                {
                    'employee_id': emp['employee_id'],
                    'employee_name': emp['employee_name'],
                    'department': emp['department']
                }
                for emp in self.new_employees
            ]

        # Add removed employees section
        if self.removed_employees:
            report['removed_employees'] = [
                {
                    'employee_id': emp['employee_id'],
                    'employee_name': emp['employee_name'],
                    'department': emp['department']
                }
                for emp in self.removed_employees
            ]

        return report
    
    def _generate_executive_summary(self, report_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate executive summary with change statistics"""
        
        total_changes = sum(len(emp['changes']) for emp in report_data.values())
        
        # Count by priority
        priority_counts = {'High': 0, 'Moderate': 0, 'Low': 0}
        for emp_data in report_data.values():
            for change in emp_data['changes']:
                priority = change.get('priority', 'Moderate')
                if priority in priority_counts:
                    priority_counts[priority] += 1
        
        return {
            'significant_changes_detected': total_changes,
            'high_priority_changes': priority_counts['High'],
            'moderate_priority_changes': priority_counts['Moderate'],
            'low_priority_changes': priority_counts['Low']
        }
    
    def _format_employee_findings(self, emp_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format findings for a single employee"""
        
        employee_section = {
            'employee_info': {
                'id': emp_data['employee_id'],
                'name': emp_data['employee_name'],
                'department': emp_data['department']
            },
            'changes': []
        }
        
        # Group changes by section
        sections = defaultdict(list)
        for change in emp_data['changes']:
            sections[change['section_name']].append(change)
        
        # Format each section
        for section_name, section_changes in sections.items():
            for change in section_changes:
                formatted_change = self._format_change_description(change)
                employee_section['changes'].append({
                    'section': section_name,
                    'item': change['item_label'],
                    'description': formatted_change,
                    'priority': change['priority'],
                    'change_type': change['change_type']
                })
        
        return employee_section
    
    def _format_change_description(self, change: Dict[str, Any]) -> str:
        """Format a change description according to business rules"""
        
        item = change['item_label']
        change_type = change['change_type']
        prev_val = change['previous_value']
        curr_val = change['current_value']
        
        if change_type == 'INCREASED':
            return f"{item} increased from {prev_val} to {curr_val}"
        elif change_type == 'DECREASED':
            return f"{item} decreased from {prev_val} to {curr_val}"
        elif change_type == 'NEW':
            return f"{item} of {curr_val} was added"
        elif change_type == 'REMOVED':
            return f"{item} of {prev_val} was removed"
        elif change_type == 'CHANGED':
            return f"{item} changed from {prev_val} to {curr_val}"
        else:
            return f"{item}: {prev_val} → {curr_val}"
    
    def _add_appendix_sections(self, report: Dict[str, Any]):
        """Add appendix sections for promotions and transfers"""

        report['appendix'] = {}

        # Promotions appendix
        if self.promotions:
            staff_promotions = [p for p in self.promotions if p['type'] == 'STAFF']
            minister_promotions = [p for p in self.promotions if p['type'] == 'MINISTER']

            if staff_promotions:
                report['appendix']['promotions_staff'] = [
                    self._format_promotion_entry(p) for p in staff_promotions
                ]

            if minister_promotions:
                report['appendix']['promotions_ministers'] = [
                    self._format_promotion_entry(p) for p in minister_promotions
                ]

        # Transfers appendix
        if self.transfers:
            staff_transfers = [t for t in self.transfers if t['type'] == 'STAFF']
            minister_transfers = [t for t in self.transfers if t['type'] == 'MINISTER']

            if staff_transfers:
                report['appendix']['transfers_staff'] = [
                    self._format_transfer_entry(t) for t in staff_transfers
                ]

            if minister_transfers:
                report['appendix']['transfers_ministers'] = [
                    self._format_transfer_entry(t) for t in minister_transfers
                ]

        # Promotions - add to main report structure for exact format
        if self.promotions:
            report['promotions'] = [
                {
                    'employee_id': promo['employee_id'],
                    'name': promo['employee_name'],
                    'department': promo['department'],
                    'type': promo['type'],
                    'rule_applied': promo.get('rule_applied', 'N/A')
                }
                for promo in self.promotions
            ]

        # Transfers - add to main report structure for exact format
        if self.transfers:
            report['transfers'] = [
                {
                    'employee_id': transfer['employee_id'],
                    'name': transfer['employee_name'],
                    'department': transfer['department'],
                    'type': transfer['type'],
                    'rule_applied': transfer.get('rule_applied', 'N/A')
                }
                for transfer in self.transfers
            ]

        # New employees - add to main report structure for exact format
        if self.new_employees:
            report['new_employees'] = [
                {
                    'employee_id': emp['employee_id'],
                    'name': emp['employee_name'],
                    'department': emp['department'],
                    'job_title': emp.get('job_title', 'N/A')
                }
                for emp in self.new_employees
            ]

        # Removed employees - add to main report structure for exact format
        if self.removed_employees:
            report['removed_employees'] = [
                {
                    'employee_id': emp['employee_id'],
                    'name': emp['employee_name'],
                    'department': emp['department'],
                    'job_title': emp.get('job_title', 'N/A')
                }
                for emp in self.removed_employees
            ]

    def _format_promotion_entry(self, promotion: Dict[str, Any]) -> str:
        """Format promotion entry for appendix"""
        base_info = f"{promotion['employee_id']}: {promotion['employee_name']} - {promotion['department']}"

        # Add detailed information if available from advanced detection
        if 'details' in promotion:
            details = promotion['details']
            prev_title = details.get('previous_title', 'Unknown')
            curr_title = details.get('current_title', 'Unknown')

            if prev_title != 'Unknown' and curr_title != 'Unknown':
                base_info += f" (From {prev_title} to {curr_title})"

                # Add salary increase info for staff promotions
                if promotion['type'] == 'STAFF' and 'salary_increase' in details:
                    salary_info = details['salary_increase']
                    if salary_info.get('increase_amount', 0) > 0:
                        base_info += f" [Salary: +{salary_info['increase_amount']:.2f} ({salary_info['increase_percentage']:.1f}%)]"

        return base_info

    def _format_transfer_entry(self, transfer: Dict[str, Any]) -> str:
        """Format transfer entry for appendix"""
        base_info = f"{transfer['employee_id']}: {transfer['employee_name']}"

        # Add detailed information if available from advanced detection
        if 'details' in transfer:
            details = transfer['details']
            prev_dept = details.get('previous_department', 'Unknown')
            curr_dept = details.get('current_department', 'Unknown')

            if prev_dept != 'Unknown' and curr_dept != 'Unknown':
                base_info += f" (From {prev_dept} to {curr_dept})"
            else:
                base_info += f" - {transfer['department']}"
        else:
            base_info += f" - {transfer['department']}"

        return base_info

def main():
    """Test the employee-based report generator"""
    
    # Test with a sample session
    generator = EmployeeBasedReportGenerator()
    
    config = {
        'generated_by': 'SAMUEL ASIEDU',
        'designation': 'PAYROLL AUDITOR'
    }
    
    # This would normally use a real session ID
    result = generator.generate_report('test_session', config)
    
    if result['success']:
        print("✅ Employee-Based Report Generator Test Passed")
        print(f"Report generated with {result['metadata']['total_employees']} employees")
    else:
        print(f"❌ Test Failed: {result['error']}")

if __name__ == "__main__":
    main()
